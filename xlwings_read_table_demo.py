import xlwings as xw
import pandas as pd
import numpy as np

def demo_xlwings_read_from_a5():
    """
    演示xlwings从A5单元格开始读取表格的各种方法
    """
    print("xlwings从A5读取表格的方法演示")
    print("=" * 50)
    
    # 创建一个示例Excel文件用于演示
    create_sample_excel()
    
    # 打开Excel应用程序（不可见）
    app = xw.App(visible=False)
    
    try:
        # 打开工作簿
        wb = app.books.open('sample_data.xlsx')
        ws = wb.sheets[0]  # 第一个工作表
        
        print("方法1: 使用expand()自动扩展范围")
        print("-" * 30)
        # 从A5开始自动扩展到包含数据的整个区域
        data1 = ws.range("A5").expand().value
        print(f"数据类型: {type(data1)}")
        print(f"数据形状: {len(data1)} 行 x {len(data1[0]) if data1 else 0} 列")
        print("前3行数据:")
        for i, row in enumerate(data1[:3]):
            print(f"  行{i+1}: {row}")
        
        print("\n方法2: 使用expand()并转换为DataFrame")
        print("-" * 30)
        # 从A5开始读取并直接转换为pandas DataFrame
        df2 = ws.range("A5").expand().options(pd.DataFrame, index=False).value
        print(f"DataFrame形状: {df2.shape}")
        print("DataFrame前3行:")
        print(df2.head(3))
        
        print("\n方法3: 使用expand()并设置表头")
        print("-" * 30)
        # 从A5开始读取，第一行作为列名
        df3 = ws.range("A5").expand().options(pd.DataFrame, header=True, index=False).value
        print(f"DataFrame形状: {df3.shape}")
        print("列名:", list(df3.columns))
        print("DataFrame前3行:")
        print(df3.head(3))
        
        print("\n方法4: 指定具体范围")
        print("-" * 30)
        # 如果知道确切范围，可以指定
        data4 = ws.range("A5:D10").value
        print(f"指定范围A5:D10的数据:")
        for i, row in enumerate(data4):
            print(f"  行{i+1}: {row}")
        
        print("\n方法5: 使用current_region")
        print("-" * 30)
        # 获取A5所在的连续数据区域
        data5 = ws.range("A5").current_region.value
        print(f"current_region数据形状: {len(data5)} 行 x {len(data5[0]) if data5 else 0} 列")
        
        print("\n方法6: 逐行读取")
        print("-" * 30)
        # 从A5开始逐行读取
        row_num = 5
        while True:
            row_data = ws.range(f"A{row_num}:D{row_num}").value
            if all(cell is None for cell in row_data):
                break
            print(f"  第{row_num}行: {row_data}")
            row_num += 1
            if row_num > 10:  # 限制演示行数
                break
        
        print("\n方法7: 读取特定列")
        print("-" * 30)
        # 只读取特定列（例如A列和C列）
        a_column = ws.range("A5:A10").value
        c_column = ws.range("C5:C10").value
        print(f"A列数据: {a_column}")
        print(f"C列数据: {c_column}")
        
        print("\n方法8: 处理空值和数据类型")
        print("-" * 30)
        # 读取数据并处理空值
        df8 = ws.range("A5").expand().options(
            pd.DataFrame, 
            index=False, 
            header=True,
            empty='',  # 空单元格用空字符串填充
            numbers=int  # 数字转换为整数
        ).value
        print("处理后的DataFrame:")
        print(df8.head(3))
        print(f"数据类型:\n{df8.dtypes}")
        
        # 关闭工作簿
        wb.close()
        
    except Exception as e:
        print(f"错误: {e}")
    finally:
        # 关闭Excel应用程序
        app.quit()

def create_sample_excel():
    """创建示例Excel文件"""
    # 创建示例数据
    data = {
        '产品代码': ['001', '002', '003', '004', '005'],
        '产品名称': ['产品A', '产品B', '产品C', '产品D', '产品E'],
        '数量': [100, 200, 150, 300, 250],
        '单价': [10.5, 20.0, 15.5, 25.0, 18.5]
    }
    
    df = pd.DataFrame(data)
    
    # 创建Excel文件
    app = xw.App(visible=False)
    wb = app.books.add()
    ws = wb.sheets[0]
    
    # 在A1-A4添加一些标题信息
    ws.range("A1").value = "公司报表"
    ws.range("A2").value = "日期: 2025-07-09"
    ws.range("A3").value = "部门: 财务部"
    ws.range("A4").value = ""  # 空行
    
    # 从A5开始写入表格数据
    ws.range("A5").value = df
    
    # 保存文件
    wb.save('sample_data.xlsx')
    wb.close()
    app.quit()

def practical_example():
    """实际应用示例：读取估值表"""
    print("\n" + "=" * 50)
    print("实际应用示例：读取估值表")
    print("=" * 50)
    
    # 模拟您的代码逻辑
    app = xw.App(visible=False)
    
    try:
        wb = app.books.open('sample_data.xlsx')
        ws = wb.sheets[0]
        
        # 读取标题信息
        table_title = ws.range("A1").value
        print(f"表格标题: {table_title}")
        
        # 从A5开始读取估值数据
        valuation_df = ws.range("A5").expand().options(pd.DataFrame, index=False, header=True).value
        print(f"估值表数据:")
        print(valuation_df)
        
        # 查找特定数据（类似您的代码）
        if '产品代码' in valuation_df.columns:
            product_001 = valuation_df.loc[valuation_df['产品代码'] == '001']
            if not product_001.empty:
                print(f"\n产品001的信息:")
                print(product_001)
        
        wb.close()
        
    except Exception as e:
        print(f"错误: {e}")
    finally:
        app.quit()

if __name__ == "__main__":
    demo_xlwings_read_from_a5()
    practical_example()
    
    print("\n" + "=" * 50)
    print("xlwings读取表格的关键参数说明:")
    print("=" * 50)
    print("• expand(): 自动扩展到包含数据的区域")
    print("• current_region: 获取连续数据区域")
    print("• options(pd.DataFrame): 直接转换为DataFrame")
    print("• header=True: 第一行作为列名")
    print("• index=False: 不使用行索引")
    print("• empty='': 空单元格的填充值")
    print("• numbers=int: 数字的数据类型转换")
    
    print("\n推荐用法（基于您的代码）:")
    print("valuation_df = ws.range('A5').expand().options(pd.DataFrame, index=False).value")
