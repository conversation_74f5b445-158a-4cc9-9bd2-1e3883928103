import pandas as pd
import numpy as np

def find_missing_account_codes(temp_fund_code_mapping_df, aum_list):
    """
    找出在temp_fund_code_mapping_df中存在但在aum_list第一列中不存在的account_code
    
    参数:
        temp_fund_code_mapping_df: DataFrame，包含account_code列
        aum_list: 列表，每个元素是[fund_code, fund_name, fund_aum, ...]格式
    
    返回:
        missing_codes: 缺失的account_code列表
        missing_details: 缺失account_code的详细信息DataFrame
    """
    
    # 获取temp_fund_code_mapping_df中的所有account_code
    expected_codes = set(temp_fund_code_mapping_df['account_code'].astype(str))
    
    # 获取aum_list第一列中的所有fund_code
    if aum_list:
        actual_codes = set(str(item[0]) for item in aum_list)
    else:
        actual_codes = set()
    
    # 找出缺失的account_code
    missing_codes = expected_codes - actual_codes
    
    if missing_codes:
        # 获取缺失account_code的详细信息
        missing_details = temp_fund_code_mapping_df[
            temp_fund_code_mapping_df['account_code'].astype(str).isin(missing_codes)
        ].copy()
        
        return list(missing_codes), missing_details
    else:
        return [], pd.DataFrame()

def analyze_aum_completeness(temp_fund_code_mapping_df, aum_list, code_type=""):
    """
    分析AUM数据的完整性
    
    参数:
        temp_fund_code_mapping_df: DataFrame，包含account_code列
        aum_list: 列表，每个元素是[fund_code, fund_name, fund_aum, ...]格式
        code_type: 代码类型描述（用于日志）
    
    返回:
        analysis_result: 分析结果字典
    """
    
    print(f"\n{'='*50}")
    print(f"分析 {code_type} AUM数据完整性")
    print(f"{'='*50}")
    
    # 基本统计
    expected_count = len(temp_fund_code_mapping_df)
    actual_count = len(aum_list)
    
    print(f"预期账户数量: {expected_count}")
    print(f"实际处理数量: {actual_count}")
    print(f"完成率: {actual_count/expected_count*100:.1f}%" if expected_count > 0 else "N/A")
    
    # 找出缺失的account_code
    missing_codes, missing_details = find_missing_account_codes(temp_fund_code_mapping_df, aum_list)
    
    if missing_codes:
        print(f"\n❌ 发现 {len(missing_codes)} 个缺失的账户代码:")
        print("-" * 30)
        for code in sorted(missing_codes):
            account_name = temp_fund_code_mapping_df.loc[
                temp_fund_code_mapping_df['account_code'].astype(str) == code, 
                'account_name'
            ].values
            name = account_name[0] if len(account_name) > 0 else "未知"
            print(f"  {code}: {name}")
        
        print(f"\n缺失账户详细信息:")
        print(missing_details[['account_code', 'account_name']].to_string(index=False))
        
    else:
        print("\n✅ 所有账户代码都已处理完成")
    
    # 检查是否有多余的数据
    if aum_list:
        expected_codes = set(temp_fund_code_mapping_df['account_code'].astype(str))
        actual_codes = [str(item[0]) for item in aum_list]
        extra_codes = [code for code in actual_codes if code not in expected_codes]
        
        if extra_codes:
            print(f"\n⚠️  发现 {len(extra_codes)} 个额外的账户代码:")
            for code in sorted(set(extra_codes)):
                count = actual_codes.count(code)
                print(f"  {code} (出现{count}次)")
    
    # 返回分析结果
    analysis_result = {
        'expected_count': expected_count,
        'actual_count': actual_count,
        'missing_codes': missing_codes,
        'missing_details': missing_details,
        'completion_rate': actual_count/expected_count if expected_count > 0 else 0
    }
    
    return analysis_result

def demo_usage():
    """演示如何使用这些函数"""
    
    # 创建示例数据
    print("创建示例数据...")
    
    # 模拟temp_fund_code_mapping_df
    temp_fund_code_mapping_df = pd.DataFrame({
        'account_code': ['001001', '001002', '001003', '001004', '001005'],
        'account_name': ['产品A', '产品B', '产品C', '产品D', '产品E'],
        'code_type': ['hq_multi', 'hq_multi', 'hq_multi', 'hq_multi', 'hq_multi']
    })
    
    # 模拟aum_list（缺少001003和001005）
    aum_list = [
        ['001001', '产品A', 1000000, 1000000],
        ['001002', '产品B', 2000000, 2000000],
        ['001004', '产品D', 4000000, 4000000],
        # 缺少 001003 和 001005
    ]
    
    print("temp_fund_code_mapping_df:")
    print(temp_fund_code_mapping_df)
    print(f"\naum_list: {len(aum_list)} 条记录")
    for item in aum_list:
        print(f"  {item}")
    
    # 方法1: 简单查找缺失代码
    print(f"\n{'='*50}")
    print("方法1: 简单查找缺失代码")
    print(f"{'='*50}")
    
    missing_codes, missing_details = find_missing_account_codes(temp_fund_code_mapping_df, aum_list)
    
    if missing_codes:
        print(f"缺失的账户代码: {missing_codes}")
        print("缺失账户详细信息:")
        print(missing_details)
    else:
        print("没有缺失的账户代码")
    
    # 方法2: 完整性分析
    analysis_result = analyze_aum_completeness(temp_fund_code_mapping_df, aum_list, "示例数据")
    
    # 方法3: 一行代码解决方案
    print(f"\n{'='*50}")
    print("方法3: 一行代码解决方案")
    print(f"{'='*50}")
    
    expected_codes = set(temp_fund_code_mapping_df['account_code'].astype(str))
    actual_codes = set(str(item[0]) for item in aum_list) if aum_list else set()
    missing_codes_simple = expected_codes - actual_codes
    
    print(f"一行代码结果: {missing_codes_simple}")

def create_completion_check_function():
    """创建可以直接插入到您代码中的函数"""
    
    function_code = '''
def check_aum_completeness(temp_fund_code_mapping_df, aum_list, code_type=""):
    """
    检查AUM数据完整性并发送警告
    
    参数:
        temp_fund_code_mapping_df: DataFrame，包含account_code列
        aum_list: 列表，每个元素是[fund_code, fund_name, fund_aum, ...]格式
        code_type: 代码类型描述
    
    返回:
        bool: True表示完整，False表示有缺失
    """
    expected_count = len(temp_fund_code_mapping_df)
    actual_count = len(aum_list)
    
    if actual_count != expected_count:
        # 找出缺失的账户代码
        expected_codes = set(temp_fund_code_mapping_df['account_code'].astype(str))
        actual_codes = set(str(item[0]) for item in aum_list) if aum_list else set()
        missing_codes = expected_codes - actual_codes
        
        # 构建警告消息
        warning_msg = f"{code_type} AUM数据不完整:\\n"
        warning_msg += f"预期: {expected_count} 个账户\\n"
        warning_msg += f"实际: {actual_count} 个账户\\n"
        
        if missing_codes:
            warning_msg += f"缺失账户代码: {', '.join(sorted(missing_codes))}"
            
            # 获取缺失账户的名称
            missing_details = []
            for code in missing_codes:
                name = temp_fund_code_mapping_df.loc[
                    temp_fund_code_mapping_df['account_code'].astype(str) == code, 
                    'account_name'
                ].values
                account_name = name[0] if len(name) > 0 else "未知"
                missing_details.append(f"{code}({account_name})")
            
            warning_msg += f"\\n缺失账户详情: {', '.join(missing_details)}"
        
        print(f"警告: {warning_msg}")
        
        # 发送Bark通知
        if 'bark_key' in globals():
            send2bark(bark_key, "AUM数据不完整", warning_msg)
        
        return False
    
    return True

# 在您的代码中使用方法:
# if len(aum_list) != len(temp_fund_code_mapping_df):
#     check_aum_completeness(temp_fund_code_mapping_df, aum_list, "Headquarter Multi")
'''
    
    print("可以插入到您代码中的函数:")
    print(function_code)

if __name__ == "__main__":
    demo_usage()
    create_completion_check_function()
    
    print(f"\n{'='*50}")
    print("总结: 在您的代码中的使用方法")
    print(f"{'='*50}")
    print("1. 简单检查:")
    print("   missing_codes = set(temp_fund_code_mapping_df['account_code'].astype(str)) - set(str(item[0]) for item in aum_list)")
    print()
    print("2. 在第59行添加:")
    print("   if len(aum_list) != len(temp_fund_code_mapping_df):")
    print("       missing_codes, missing_details = find_missing_account_codes(temp_fund_code_mapping_df, aum_list)")
    print("       print(f'缺失的账户代码: {missing_codes}')")
    print("       send2bark(bark_key, 'AUM数据不完整', f'缺失账户: {missing_codes}')")
    print()
    print("3. 完整性检查函数:")
    print("   check_aum_completeness(temp_fund_code_mapping_df, aum_list, 'Headquarter Multi')")
