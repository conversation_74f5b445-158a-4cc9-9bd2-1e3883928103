import pandas as pd
import numpy as np

def demo_search_dataframe():
    """演示在没有表头的DataFrame中查找特定数据的方法"""
    
    # 创建示例数据（模拟估值表）
    data = [
        ['科目代码', '科目名称', '期初余额', '本期借方', '本期贷方', '期末余额', '数量', '单价', '市值', '占比', '备注', 1000000],
        ['1001', '库存现金', 50000, 0, 0, 50000, 0, 0, 50000, 5.0, '', 50000],
        ['1002', '银行存款', 200000, 100000, 50000, 250000, 0, 0, 250000, 25.0, '', 250000],
        ['1101', '股票投资', 300000, 200000, 0, 500000, 1000, 500, 500000, 50.0, '', 500000],
        ['1201', '债券投资', 150000, 50000, 0, 200000, 200, 1000, 200000, 20.0, '', 200000],
        ['资产合计', '', 700000, 350000, 50000, 1000000, 0, 0, 1000000, 100.0, '', 1000000],
        ['2001', '应付账款', 0, 0, 0, 0, 0, 0, 0, 0, '', 0],
        ['负债合计', '', 0, 0, 0, 0, 0, 0, 0, 0, '', 0],
        ['净资产', '', 700000, 350000, 50000, 1000000, 0, 0, 1000000, 100.0, '', 1000000]
    ]
    
    # 创建DataFrame（没有表头）
    df = pd.DataFrame(data)
    
    print("示例DataFrame（没有表头）:")
    print(df)
    print(f"DataFrame形状: {df.shape}")
    print()
    
    # 方法1: 使用iloc进行位置索引
    print("方法1: 使用iloc进行位置索引")
    print("-" * 40)
    
    # 查找第一列（索引0）等于"资产合计"的行
    mask = df.iloc[:, 0] == '资产合计'
    matching_rows = df[mask]
    
    if not matching_rows.empty:
        # 获取第11列（索引10）的值
        value_method1 = matching_rows.iloc[0, 11]  # 第11列，索引为10
        print(f"找到'资产合计'行，第11列的值: {value_method1}")
        print(f"完整行数据: {matching_rows.iloc[0].tolist()}")
    else:
        print("未找到'资产合计'行")
    
    print()
    
    # 方法2: 使用loc和布尔索引
    print("方法2: 使用loc和布尔索引")
    print("-" * 40)
    
    # 查找第一列等于"资产合计"的行的第11列
    try:
        value_method2 = df.loc[df.iloc[:, 0] == '资产合计', 11].values[0]
        print(f"使用loc方法获取的值: {value_method2}")
    except IndexError:
        print("未找到匹配的行")
    
    print()
    
    # 方法3: 使用query方法（需要给列命名）
    print("方法3: 使用query方法")
    print("-" * 40)
    
    # 临时给列命名
    df_temp = df.copy()
    df_temp.columns = [f'col_{i}' for i in range(len(df.columns))]
    
    try:
        value_method3 = df_temp.query("col_0 == '资产合计'")['col_11'].values[0]
        print(f"使用query方法获取的值: {value_method3}")
    except IndexError:
        print("未找到匹配的行")
    
    print()
    
    # 方法4: 使用循环查找（适合复杂条件）
    print("方法4: 使用循环查找")
    print("-" * 40)
    
    value_method4 = None
    for index, row in df.iterrows():
        if row.iloc[0] == '资产合计':  # 第一列
            value_method4 = row.iloc[11]  # 第11列
            print(f"在索引{index}找到'资产合计'，第11列的值: {value_method4}")
            break
    
    if value_method4 is None:
        print("未找到'资产合计'行")
    
    print()
    
    # 方法5: 一行代码解决（推荐）
    print("方法5: 一行代码解决（推荐）")
    print("-" * 40)
    
    try:
        # 这是最简洁的方法
        fund_aum = df.loc[df.iloc[:, 0] == '资产合计', 11].values[0]
        print(f"资产合计的第11列数值: {fund_aum}")
        print("代码: df.loc[df.iloc[:, 0] == '资产合计', 11].values[0]")
    except IndexError:
        print("未找到匹配的行")
    
    print()
    
    # 方法6: 处理可能的异常情况
    print("方法6: 安全的查找方法（处理异常）")
    print("-" * 40)
    
    def safe_find_value(df, search_col, search_value, target_col):
        """
        安全地在DataFrame中查找值
        
        Args:
            df: DataFrame
            search_col: 搜索的列索引
            search_value: 要搜索的值
            target_col: 目标列索引
        
        Returns:
            找到的值或None
        """
        try:
            mask = df.iloc[:, search_col] == search_value
            matching_rows = df[mask]
            
            if matching_rows.empty:
                print(f"未找到包含'{search_value}'的行")
                return None
            
            if len(matching_rows) > 1:
                print(f"找到多行包含'{search_value}'，使用第一行")
            
            if target_col >= len(df.columns):
                print(f"目标列索引{target_col}超出范围")
                return None
            
            return matching_rows.iloc[0, target_col]
            
        except Exception as e:
            print(f"查找过程中出错: {e}")
            return None
    
    result = safe_find_value(df, 0, '资产合计', 11)
    print(f"安全查找结果: {result}")
    
    print()
    
    # 方法7: 查找多个条件
    print("方法7: 查找多个条件")
    print("-" * 40)
    
    # 查找所有包含"合计"的行
    total_rows = df[df.iloc[:, 0].str.contains('合计', na=False)]
    print("所有包含'合计'的行:")
    for idx, row in total_rows.iterrows():
        print(f"  {row.iloc[0]}: 第11列值 = {row.iloc[11]}")

def test_with_your_data_structure():
    """测试您的代码结构"""
    print("\n" + "="*50)
    print("测试您的代码结构")
    print("="*50)
    
    # 模拟您的valuation_df
    # 假设这是从xlwings读取的数据
    valuation_data = [
        ['科目代码', '科目名称', '期初', '借方', '贷方', '期末', '数量', '单价', '市值', '占比', '备注', 1000000],
        ['1001', '现金', 100000, 0, 0, 100000, 0, 0, 100000, 10, '', 100000],
        ['1002', '银行存款', 200000, 0, 0, 200000, 0, 0, 200000, 20, '', 200000],
        ['资产合计', '', 300000, 0, 0, 300000, 0, 0, 300000, 30, '', 300000],
        ['负债合计', '', 0, 0, 0, 0, 0, 0, 0, 0, '', 0]
    ]
    
    valuation_df = pd.DataFrame(valuation_data)
    
    print("模拟的valuation_df:")
    print(valuation_df)
    print()
    
    # 您当前的代码逻辑
    print("您当前的代码逻辑:")
    print("fund_aum = valuation_df.loc[valuation_df['科目代码'] == '资产合计', 11].values[0]")
    print()
    
    # 但是如果没有表头，应该这样写：
    print("没有表头时的正确写法:")
    print("fund_aum = valuation_df.loc[valuation_df.iloc[:, 0] == '资产合计', 11].values[0]")
    
    try:
        # 修正后的代码
        fund_aum = valuation_df.loc[valuation_df.iloc[:, 0] == '资产合计', 11].values[0]
        print(f"结果: fund_aum = {fund_aum}")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    demo_search_dataframe()
    test_with_your_data_structure()
    
    print("\n" + "="*50)
    print("总结：推荐的方法")
    print("="*50)
    print("1. 简单查找:")
    print("   fund_aum = df.loc[df.iloc[:, 0] == '资产合计', 11].values[0]")
    print()
    print("2. 安全查找:")
    print("   mask = df.iloc[:, 0] == '资产合计'")
    print("   if mask.any():")
    print("       fund_aum = df.loc[mask, 11].values[0]")
    print("   else:")
    print("       print('未找到资产合计行')")
    print()
    print("3. 您的代码修正:")
    print("   # 原代码（有表头时）:")
    print("   # fund_aum = valuation_df.loc[valuation_df['科目代码'] == '资产合计', 11].values[0]")
    print("   ")
    print("   # 修正代码（无表头时）:")
    print("   fund_aum = valuation_df.loc[valuation_df.iloc[:, 0] == '资产合计', 11].values[0]")
