import os
import re
import datetime
import tempfile
import time
from pathlib import Path

def create_test_files():
    """创建测试文件用于演示"""
    # 创建临时目录
    test_dir = tempfile.mkdtemp(prefix="file_search_test_")
    print(f"创建测试目录: {test_dir}")
    
    # 创建不同日期的测试文件
    test_files = [
        "估值表-中欧基金安泰美元债1号集合资产管理计划（QDII）-20250703.xls",
        "估值表-中欧基金安泰美元债2号集合资产管理计划-20250704.xls", 
        "instOf_7b9297f6-acf6-46cb-9895-e65d419ebdb7.XLSX",
        "report_20250703.csv",
        "data_20250704.txt"
    ]
    
    created_files = []
    for i, filename in enumerate(test_files):
        file_path = os.path.join(test_dir, filename)
        
        # 创建文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"测试文件内容 - {filename}")
        
        # 修改文件的创建时间（模拟不同日期创建的文件）
        if i < 2:  # 前两个文件设置为20250703
            target_time = datetime.datetime(2025, 7, 3, 10, 0, 0).timestamp()
        elif i < 4:  # 接下来两个文件设置为20250704  
            target_time = datetime.datetime(2025, 7, 4, 10, 0, 0).timestamp()
        else:  # 最后一个文件设置为今天
            target_time = datetime.datetime.now().timestamp()
        
        # 设置文件的访问时间和修改时间
        os.utime(file_path, (target_time, target_time))
        created_files.append(file_path)
        
        print(f"创建文件: {filename}")
    
    return test_dir, created_files

def demo_original_function():
    """演示原始函数的功能"""
    print("\n" + "="*60)
    print("演示原始 find_specific_files_by_date 函数")
    print("="*60)
    
    test_dir, _ = create_test_files()
    
    # 测试不同的正则表达式
    test_cases = [
        {
            "regex": r"^估值表-中欧基金.*资产管理计划.*-{target_date}\.xls$",
            "target_date": "20250703",
            "description": "查找20250703的估值表文件"
        },
        {
            "regex": r"^instOf_[a-fA-F0-9-]+\.XLSX$",
            "target_date": "20250704", 
            "description": "查找20250704的instOf文件"
        },
        {
            "regex": r".*{target_date}.*",
            "target_date": "20250703",
            "description": "查找包含20250703的所有文件"
        }
    ]
    
    for case in test_cases:
        print(f"\n测试: {case['description']}")
        print(f"正则表达式: {case['regex']}")
        print(f"目标日期: {case['target_date']}")
        
        try:
            # 这里需要导入您的函数
            # from PublicFunc import find_specific_files_by_date
            # result = find_specific_files_by_date(test_dir, case['regex'], case['target_date'])
            
            # 模拟函数调用结果
            result = simulate_find_specific_files_by_date(test_dir, case['regex'], case['target_date'])
            
            if result:
                print(f"找到 {len(result)} 个文件:")
                for file_path in result:
                    print(f"  - {os.path.basename(file_path)}")
            else:
                print("未找到匹配的文件")
                
        except Exception as e:
            print(f"错误: {e}")
    
    # 清理测试文件
    import shutil
    shutil.rmtree(test_dir)

def simulate_find_specific_files_by_date(root_dir, regex, target_date):
    """模拟修改后的函数功能"""
    import os
    import re
    import datetime

    # 参数验证
    if not re.match(r"^\d{8}$", target_date):
        raise ValueError("日期格式必须为8位数字（YYYYMMDD）")

    # 转换目标日期
    try:
        target_datetime = datetime.datetime.strptime(target_date, "%Y%m%d")
        target_date_only = target_datetime.date()
    except ValueError:
        raise ValueError(f"无效的日期格式: {target_date}")

    # 处理正则表达式
    processed_regex = regex.format(target_date=target_date)
    pattern = re.compile(processed_regex, re.IGNORECASE)

    matched_files = []

    # 搜索文件
    for dirpath, _, filenames in os.walk(root_dir):
        for name in filenames:
            if pattern.match(name):
                file_path = os.path.join(dirpath, name)
                
                try:
                    # 获取文件创建时间
                    file_stat = os.stat(file_path)
                    if hasattr(file_stat, 'st_birthtime'):
                        creation_time = file_stat.st_birthtime
                    else:
                        creation_time = file_stat.st_ctime
                    
                    file_creation_date = datetime.datetime.fromtimestamp(creation_time).date()
                    
                    # 检查创建日期是否匹配
                    if file_creation_date == target_date_only:
                        matched_files.append(file_path)
                        
                except (OSError, IOError) as e:
                    print(f"警告: 无法获取文件 {file_path} 的创建时间: {e}")
                    continue

    return matched_files if matched_files else None

def demo_advanced_function():
    """演示高级版本函数的功能"""
    print("\n" + "="*60)
    print("演示高级版本 find_files_by_date_advanced 函数")
    print("="*60)
    
    test_dir, _ = create_test_files()
    
    # 测试高级功能
    test_cases = [
        {
            "regex": r"^估值表.*\.xls$",
            "target_date": "20250703",
            "date_type": "modified",
            "tolerance_days": 0,
            "description": "精确匹配修改日期为20250703的估值表文件"
        },
        {
            "regex": r".*\.XLSX$",
            "target_date": "20250704",
            "date_type": "creation", 
            "tolerance_days": 1,
            "description": "匹配创建日期在20250704前后1天的XLSX文件"
        }
    ]
    
    for case in test_cases:
        print(f"\n测试: {case['description']}")
        print(f"正则表达式: {case['regex']}")
        print(f"目标日期: {case['target_date']}")
        print(f"日期类型: {case['date_type']}")
        print(f"容差天数: {case['tolerance_days']}")
        
        try:
            result = simulate_find_files_by_date_advanced(
                test_dir, 
                case['regex'], 
                case['target_date'],
                case['date_type'],
                case['tolerance_days']
            )
            
            if result:
                print(f"找到 {len(result)} 个文件:")
                for file_info in result:
                    print(f"  - {file_info['name']}")
                    print(f"    创建日期: {file_info['creation_date']}")
                    print(f"    修改日期: {file_info['modified_date']}")
                    print(f"    日期差异: {file_info['date_diff']} 天")
            else:
                print("未找到匹配的文件")
                
        except Exception as e:
            print(f"错误: {e}")
    
    # 清理测试文件
    import shutil
    shutil.rmtree(test_dir)

def simulate_find_files_by_date_advanced(root_dir, regex, target_date, date_type='creation', tolerance_days=0):
    """模拟高级版本函数功能"""
    import os
    import re
    import datetime

    # 参数验证
    if not re.match(r"^\d{8}$", target_date):
        raise ValueError("日期格式必须为8位数字（YYYYMMDD）")
    
    if date_type not in ['creation', 'modified', 'accessed']:
        raise ValueError("date_type必须是'creation', 'modified', 或'accessed'")

    # 转换目标日期
    try:
        target_datetime = datetime.datetime.strptime(target_date, "%Y%m%d")
        target_date_only = target_datetime.date()
    except ValueError:
        raise ValueError(f"无效的日期格式: {target_date}")

    # 处理正则表达式
    processed_regex = regex.format(target_date=target_date)
    pattern = re.compile(processed_regex, re.IGNORECASE)

    matched_files = []

    # 搜索文件
    for dirpath, _, filenames in os.walk(root_dir):
        for name in filenames:
            if pattern.match(name):
                file_path = os.path.join(dirpath, name)
                
                try:
                    file_stat = os.stat(file_path)
                    
                    # 根据date_type选择时间戳
                    if date_type == 'creation':
                        if hasattr(file_stat, 'st_birthtime'):
                            timestamp = file_stat.st_birthtime
                        else:
                            timestamp = file_stat.st_ctime
                    elif date_type == 'modified':
                        timestamp = file_stat.st_mtime
                    else:  # accessed
                        timestamp = file_stat.st_atime
                    
                    file_date = datetime.datetime.fromtimestamp(timestamp).date()
                    
                    # 检查日期容差
                    date_diff = abs((file_date - target_date_only).days)
                    if date_diff <= tolerance_days:
                        file_info = {
                            'path': file_path,
                            'name': name,
                            'creation_date': datetime.datetime.fromtimestamp(file_stat.st_ctime).date(),
                            'modified_date': datetime.datetime.fromtimestamp(file_stat.st_mtime).date(),
                            'accessed_date': datetime.datetime.fromtimestamp(file_stat.st_atime).date(),
                            'size': file_stat.st_size,
                            'date_diff': date_diff
                        }
                        matched_files.append(file_info)
                        
                except (OSError, IOError) as e:
                    print(f"警告: 无法获取文件 {file_path} 的信息: {e}")
                    continue

    # 按日期差异排序
    matched_files.sort(key=lambda x: x['date_diff'])
    
    return matched_files if matched_files else None

if __name__ == "__main__":
    print("文件搜索功能演示")
    print("="*60)
    
    demo_original_function()
    demo_advanced_function()
    
    print("\n" + "="*60)
    print("函数使用说明")
    print("="*60)
    print("1. 基础版本 find_specific_files_by_date:")
    print("   - 同时匹配文件名正则表达式和文件创建日期")
    print("   - 返回匹配文件的路径列表")
    print()
    print("2. 高级版本 find_files_by_date_advanced:")
    print("   - 支持选择日期类型（创建/修改/访问）")
    print("   - 支持日期容差（前后几天）")
    print("   - 返回详细的文件信息")
    print()
    print("主要改进:")
    print("   ✓ 增加了文件创建日期匹配")
    print("   ✓ 更好的错误处理")
    print("   ✓ 支持多种日期类型")
    print("   ✓ 支持日期容差")
    print("   ✓ 返回详细文件信息")
