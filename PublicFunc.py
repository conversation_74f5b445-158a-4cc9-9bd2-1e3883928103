
import numpy as np
import pandas as pd
import os
import sys
from threading import Timer
from datetime import *
import datetime as dt
import time
import json
import xlwings as xlw
from xlwings import constants as win32c
# from xlwings.constants import PivotTableSourceType
import calendar
from sqlalchemy import *
from pyDes import *
import base64
import urllib3
import requests
# import hashlib
# from Crypto.Hash import SHA1
# from Crypto.Signature import pkcs1_15
# from Crypto.PublicKey import RSA
# from Crypto.Cipher import AES
import re
import smtplib
from email.mime.text import MIMEText  # 发送邮件内容为文本形式时导入
from email.header import Header  # 给邮件设置标题时导入
import email
import mimetypes
from email.mime.multipart import MIMEMultipart
from email.mime.image import MIMEImage
from email.mime.base import MIMEBase
import configparser
import warnings
import win32com.client
from dateutil.relativedelta import relativedelta
import importlib
# from openpyxl import load_workbook
# from openpyxl.styles import Font
# from openpyxl.utils.dataframe import dataframe_to_rows
# from openpyxl.utils import get_column_letter
import pandas_market_calendars as mcal
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger




Des_Key = "UBPSHNAV"  # Key
Des_IV = "46238239"  # 自定IV向量

PER_PAGE = 50

glb_assign_date = ''
glb_fund_name = ''
glb_customer_name = ''
glb_sales_name = ''
glb_table_data = []
ALLOWED_EXTENSIONS = {'csv', 'xlsx'}
INDUSTRY_NEUTRAL_ALLOWED_EXTENSIONS = {'csv', 'xlsx', 'zip'}
VALUATION_STATEMENT_ALLOWED_EXTENSIONS = {'zip'}
VALUATION_MAPPING_ALLOWED_EXTENSIONS = {'xlsx'}
table_columns = ['Date', 'Fund Name', 'Customer Name', 'Sales Name']

# 缓存配置
# 设置缓存时间为 60 秒
cache_timeout = 60
cache = {}

os.environ["PYTHONUTF8"] = "1"
# sys.stdout = sys.__stdout__

# 显示所有列
pd.set_option('display.max_columns', None)

# 或者如果你想要设置最大列数为某个特定值而不是无限制，可以这样做：
# pd.set_option('display.max_columns', 100)  # 例如显示最多100列

# 显示完整的DataFrame内容，不截断
pd.set_option('display.expand_frame_repr', False)

# 如果你还想确保所有的行都显示出来，而不仅仅是前几行和后几行，你可以这样设置：
pd.set_option('display.max_rows', None)


def DesDecrypt(encode, key=None, iv=None):
    if key is None:
        key = Des_Key
    if iv is None:
        iv = Des_IV

    k = des(key, CBC, iv, pad=None, padmode=PAD_PKCS5)
    b64str = base64.b64decode(encode)
    return k.decrypt(b64str)


def isNaNo(sth):
    '''
    NaN、None或者空字符串返回True，其他情况返回False
    '''
    if sth is None:
        return True
    if isinstance(sth, float):
        if np.isnan(sth):
            return True
    return False


def is_valid_date(date_str, date_format="%Y-%m-%d"):
    try:
        # 尝试将输入的字符串解析为日期
        dt.datetime.strptime(date_str, date_format)
        return True
    except ValueError:
        return False


def days_in_year(year):
    start = dt.datetime(year, 1, 1)
    end = dt.datetime(year + 1, 1, 1)
    return (end - start).days


def allowed_file(filename):
    return '.' in filename and \
        filename.rsplit('.', 1)[1] in ALLOWED_EXTENSIONS


def industry_neutral_allowed_file(filename):
    return '.' in filename and \
        filename.rsplit('.', 1)[1] in INDUSTRY_NEUTRAL_ALLOWED_EXTENSIONS


def valuation_statement_allowed_file(filename):
    return '.' in filename and \
        filename.rsplit('.', 1)[1] in VALUATION_STATEMENT_ALLOWED_EXTENSIONS


def valuation_mapping_allowed_file(filename):
    return '.' in filename and \
        filename.rsplit('.', 1)[1] in VALUATION_MAPPING_ALLOWED_EXTENSIONS


def days_count_of_year(years):
    return 366 if calendar.isleap(int(str(years))) else 365


def days_between_date(begin_date, finish_date):
    date_list = []
    begin_date = dt.datetime.strptime(begin_date, "%Y-%m-%d")
    finish_date = dt.datetime.strptime(finish_date, "%Y-%m-%d")
    while begin_date <= finish_date:
        date_str = begin_date.strftime("%Y-%m-%d")
        date_list.append(date_str)
        begin_date += dt.timedelta(days=1)
    return date_list


def count_between_date(day1, day2):
    time_array1 = time.strptime(day1, "%Y-%m-%d")
    timestamp_day1 = int(time.mktime(time_array1))
    time_array2 = time.strptime(day2, "%Y-%m-%d")
    timestamp_day2 = int(time.mktime(time_array2))
    result = (timestamp_day2 - timestamp_day1) // 60 // 60 // 24
    return result


def get_last_day_of_previous_month(current_date):
    current_date = dt.datetime.strptime(current_date, "%Y-%m-%d")

    # 计算上一个月的第一天
    first_day_of_this_month = current_date.replace(day=1)
    last_day_of_previous_month = first_day_of_this_month - timedelta(days=1)

    # 返回上个月的最后一天
    return last_day_of_previous_month


def get_last_day_of_month(date):
    # 将输入日期转换为 Pandas 的 Period 对象
    period = pd.Period(date, freq='M')
    # 获取该月的最后一天
    last_day = period.end_time.date()
    return last_day


def find_rows_in_sheet_openpyxl(find_str, ws, col, start_row, end_row):
    for row in range(start_row, end_row + 1):
        cell = ws.cell(row=row, column=col)  # 第二列的列号为2
        if str(find_str) in str(cell.value).strip():
            # 如果找到包含 'abc' 的单元格，记录行号
            return row
    # 如果没有找到，返回 None
    return None


def find_rows_in_sheet_xlrd(find_str, ws, col, start_row, end_row):
    for row in range(start_row, end_row + 1):
        if str(find_str) in str(ws.cell_value(row, col)).strip():
            # 如果找到包含 'abc' 的单元格，记录行号
            return row
    # 如果没有找到，返回 None
    return None


def find_cols_in_sheet_openpyxl(find_str, ws, row, start_col, end_col):
    for col in range(start_col, start_col + 1):
        cell = ws.cell(row=row, column=col)  # 第二列的列号为2
        if str(find_str) in str(cell.value).strip():
            # 如果找到包含 'abc' 的单元格，记录行号
            return col
    # 如果没有找到，返回 None
    return None


def find_cols_in_sheet_xlrd(find_str, ws, row, start_col, end_col):
    for col in range(start_col, start_col + 1):
        if str(find_str) in str(ws.cell_value(row, col)).strip():
            # 如果找到包含 'abc' 的单元格，记录行号
            return col
    # 如果没有找到，返回 None
    return None


def get_last_cell_in_column_openpyxl(ws, col):
    # 转换列的数字索引为字母
    # column_letter = openpyxl.utils.get_column_letter(column_index)
    max_row = ws.max_row
    while max_row > 0 and ws[f"{col}{max_row}"] is None:
        max_row -= 1

    # 如果 max_row 是 0，意味着整列都是空的
    if max_row == 0:
        return None
    else:
        return max_row


def get_last_cell_in_column_xlrd(ws, col):
    # 转换列的数字索引为字母
    # column_letter = openpyxl.utils.get_column_letter(column_index)
    max_row = ws.nrows - 1
    while max_row > 0:
        cell_value = ws.cell_value(max_row, col)
        if cell_value != '':
            break
        max_row -= 1

    # 如果 max_row 是 0，意味着整列都是空的
    if max_row == 0:
        return None
    else:
        return max_row


def hex_to_rgb(hex_color):
    hex_color = hex_color.lstrip('#')
    return tuple(int(hex_color[i:i + 2], 16) for i in (0, 2, 4))


def sendEmail(authInfo, fromAdd, toAdd, subject, plainText, htmlText, attPath):
    strFrom = fromAdd
    strTo = ', '.join(toAdd)

    server = authInfo.get('server')
    port = authInfo.get('port')
    user = authInfo.get('user')
    passwd = authInfo.get('password')

    # if not (server and user and passwd):
    #     print('incomplete login info, exit now')
        # return

    # 设定root信息
    msgRoot = MIMEMultipart('related')
    msgRoot['Subject'] = subject
    msgRoot['From'] = strFrom
    # msgRoot['To'] = strTo
    msgRoot['Bcc'] = strTo
    msgRoot.preamble = 'This is a multi-part message in MIME format.'

    # Encapsulate the plain and HTML versions of the message body in an
    # 'alternative' part, so message agents can decide which they want to display.
    msgAlternative = MIMEMultipart('alternative')
    msgRoot.attach(msgAlternative)

    # 设定纯文本信息
    msgText = MIMEText(plainText, 'plain', 'utf-8')
    msgAlternative.attach(msgText)

    # 设定HTML信息
    msgText = MIMEText(htmlText, 'html', 'utf-8')
    msgAlternative.attach(msgText)

    # # 设定内置图片信息
    # fp = open('test.jpg', 'rb')
    # msgImage = MIMEImage(fp.read())
    # fp.close()
    # msgImage.add_header('Content-ID', '<image1>')
    # msgRoot.attach(msgImage)

    if attPath != "":
        # send_file = open(attPath, "rb").read()
        #
        # att = MIMEText(send_file, "base64", 'utf-8')
        # att['Content-Type'] = 'application/octet-stream'
        # attPathSplit = attPath.split('\\')
        # att['Content-Disposition'] = 'attachment;filename=' + os.path.basename(attPathSplit[len(attPathSplit) - 1])
        # msgRoot.attach(att)

        # 构造MIMEBase对象做为文件附件内容并附加到根容器
        contype = 'application/octet-stream'
        maintype, subtype = contype.split('/', 1)

        ## 读入文件内容并格式化
        data = open(attPath, 'rb')
        file_msg = MIMEBase(maintype, subtype)
        file_msg.set_payload(data.read())
        data.close()
        email.encoders.encode_base64(file_msg)

        ## 设置附件头
        basename = os.path.basename(attPath)
        file_msg.add_header('Content-Disposition',
                            'attachment', filename=basename)
        msgRoot.attach(file_msg)

    # 发送邮件
    smtp = smtplib.SMTP(server)
    # 设定调试级别，依情况而定
    # smtp.set_debuglevel(1)
    smtp.connect(server, port)
    # smtp.login(user, passwd)
    smtp.sendmail(strFrom, toAdd, msgRoot.as_string())
    smtp.quit()
    return


def send2bark(key, title, content):
    bl = "https://api.day.app/" + key
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    try:
        msg = "{0}/{1}/{2}/?isArchive=1".format(bl, title, content)
        link = msg
        res = requests.get(link, verify=False)
    except Exception as e:
        print('Reason:', e)
        return
    return


def once_per_day(func):
    """装饰器：确保函数每天只执行一次成功"""
    last_run_date = [None]
    success_today = [False]
    
    def wrapper(*args, **kwargs):
        current_date = dt.datetime.now().date()
        
        # 如果是新的一天，重置状态
        if current_date != last_run_date[0]:
            last_run_date[0] = current_date
            success_today[0] = False
        
        # 如果今天已经成功执行过，直接返回
        if success_today[0]:
            print(f"Job has been executed today, and will not be executed again.")
            return None
        
        # 执行原函数
        result = func(*args, **kwargs)
        
        # 如果返回0，标记今天已成功
        if result == 0:
            success_today[0] = True
            print(f"Job executed successfully at {dt.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        # elif result == -99:
        #     print("Trade file is not ready or no trades today, and Nav file data has been processed and email notification has been already sent...")
        
        return result
    
    return wrapper

def start_scheduler(my_function):
    """启动定时任务"""
    # 应用装饰器
    decorated_function = once_per_day(my_function)
    
    # 读取配置
    config = configparser.ConfigParser()
    config.read('config.ini', encoding='utf-8')

    start_time_str = config.get('base_config', 'start_time')
    end_time_str = config.get('base_config', 'end_time')
    scan_interval = config.getint('base_config', 'scan_interval')

    # 获取交易所日历名称，如果配置中没有，默认使用HKEX
    calendar_name = config.get('base_config', 'calendar_name', fallback='HKEX')

    # 解析时间
    start_time = dt.datetime.strptime(start_time_str, "%H:%M:%S").time()
    end_time = dt.datetime.strptime(end_time_str, "%H:%M:%S").time()
    
    # 检查是否为跨天时间范围
    is_overnight = start_time > end_time

    # 创建调度器
    scheduler = BackgroundScheduler()

    # 记录上次执行时间
    last_execution_time = [None]
    next_execution_time = [dt.datetime.now()]

    # 添加定时任务，在指定时间范围内每隔指定秒数执行
    def job_function():
        current_time = dt.datetime.now().time()
        current_date = dt.datetime.now().date()

        # 更新上次执行时间
        last_execution_time[0] = dt.datetime.now()
        # 计算下次执行时间
        next_execution_time[0] = last_execution_time[0] + dt.timedelta(seconds=scan_interval)

        # 检查是否为交易日
        today_is_trading_day = is_trading_day(current_date, calendar_name)
        
        # # 如果是跨天时间范围，还需要检查昨天是否为交易日
        # yesterday_is_trading_day = False
        # if is_overnight and current_time < end_time:
        #     yesterday = current_date - dt.timedelta(days=1)
        #     yesterday_is_trading_day = is_trading_day(yesterday, calendar_name)

        print("————————————————————————————————————————————————————————————————")
        print(f"Current Time: {dt.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Next Execution Time: {next_execution_time[0].strftime('%Y-%m-%d %H:%M:%S')} (Interval: {scan_interval} seconds)")

        # 判断是否在执行时间范围内
        in_time_range = False
        if is_overnight:
            # 跨天情况：当前时间大于等于开始时间 或 当前时间小于等于结束时间
            in_time_range = (current_time >= start_time) or (current_time <= end_time)
            # # 还需要考虑交易日的情况
            # trading_day = (today_is_trading_day and current_time >= start_time) or \
            #              (yesterday_is_trading_day and current_time <= end_time)
            # 只考虑当天是否是交易日
            trading_day = today_is_trading_day
        else:
            # 非跨天情况：当前时间在开始时间和结束时间之间
            in_time_range = start_time <= current_time <= end_time
            trading_day = today_is_trading_day

        if trading_day and in_time_range:
            print(f"Current time is within trading hours, executing task...")
            decorated_function()
        else:
            if not trading_day:
                print(f"Not a valid trading day for current time, skip task.")
            else:
                print(f"Current time {current_time.strftime('%H:%M:%S')} is not within trading hours {start_time_str} - {end_time_str}, skip task.")

    # 显示倒计时的函数
    def show_countdown():
        while True:
            # 如果已经设置了下次执行时间
            if next_execution_time[0]:
                now = dt.datetime.now()
                remaining = (next_execution_time[0] - now).total_seconds()

                # 只有当剩余时间为整数秒时才更新显示
                if int(remaining) == remaining:
                    # 使用\r来覆盖同一行
                    print(f"\rCountdown to next execution: {int(remaining)}seconds", end="", flush=True)

                # 如果已经到了或超过了执行时间，清空显示
                if remaining <= 0:
                    print("\r                                 ", end="", flush=True)

            # 短暂休眠以减少CPU使用
            time.sleep(0.1)

    # 使用间隔触发器，每隔scan_interval秒执行一次
    scheduler.add_job(
        job_function,
        IntervalTrigger(seconds=scan_interval),
        id='my_job',
        # next_run_time = dt.datetime.now()  # 设置下一次运行时间为现在，使其立即执行
    )

    # 添加每天重置任务的作业
    def reset_job():
        current_date = dt.datetime.now().date()
        trading_day = is_trading_day(current_date, calendar_name)

        if trading_day:
            print(f"Reset job at: {dt.datetime.now().strftime('%Y-%m-%d %H:%M:%S')} (Trading Day)")
            # 可以在这里添加每天开始时的初始化逻辑
        else:
            print(f"Today {current_date.strftime('%Y-%m-%d')} is not a trading day, skip reset job.")

    # 使用cron触发器，每天在start_time时执行
    hour, minute, second = start_time_str.split(':')
    scheduler.add_job(
        reset_job,
        CronTrigger(hour=int(hour), minute=int(minute), second=int(second)),
        id='reset_job',
        coalesce=True
    )

    # 启动调度器
    scheduler.start()
    print(f"Scheduler started, will execute task between {start_time_str} - {end_time_str} every {scan_interval} seconds.")
    print(f"Exchange Calendar: {calendar_name}")

    # 方法1：在启动调度器后立即手动执行一次任务
    job_function()

    # 启动倒计时显示线程
    import threading
    countdown_thread = threading.Thread(target=show_countdown, daemon=True)
    countdown_thread.start()

    try:
        # 保持主线程运行
        while True:
            time.sleep(1)
    except (KeyboardInterrupt, SystemExit):
        # 关闭调度器
        scheduler.shutdown()
        print("\nScheduler stopped...")


def calculate_text_width(text):
    # 中文字符宽度为2，其他字符宽度为1
    chinese_pattern = re.compile(r'[\u4e00-\u9fa5\u3000-\u303F\uFF01-\uFFEF]')
    width = 0
    for char in text:
        if chinese_pattern.match(char):
            width += 2
        else:
            width += 1
    return width


def is_trading_day(date=None, calendar_name='HKEX'):
    """
    检查指定日期是否为交易日

    参数:
        date: 要检查的日期，默认为当前日期
        calendar_name: 交易所日历名称，默认为香港交易所

    返回:
        bool: 如果是交易日返回True，否则返回False
    """
    if date is None:
        date = dt.datetime.now().date()
    else:
        date = pd.Timestamp(date).date()

    # 获取交易所日历
    calendar = mcal.get_calendar(calendar_name)

    # 获取指定日期的交易时间
    schedule = calendar.schedule(start_date=date, end_date=date)

    # 如果日程表为空，则不是交易日
    return len(schedule) > 0


def get_trading_day(date_str, calendar_name='HKEX', diff=0):
    """
    获取指定日期相对于diff的交易日

    参数:
        date_str (str/datetime/Timestamp): 指定日期
        calendar_name (str): 交易所日历名称，默认为香港交易所
        diff (int): 相对偏移量，负数表示前几个交易日，0表示过去的最近交易日，正数表示后几个交易日

    返回:
        date: 目标交易日，返回datetime.date类型
    """
    # 获取交易所日历
    calendar = mcal.get_calendar(calendar_name)

    # 确保日期是Timestamp类型
    date = pd.Timestamp(date_str)

    # 如果diff为0，返回当前日期如果是交易日，否则返回最近的前一个交易日
    if diff == 0:
        # 检查当前日期是否为交易日
        schedule = calendar.schedule(start_date=date, end_date=date)
        if len(schedule) > 0:
            return date.date()
        else:
            # 不是交易日，返回前一个交易日
            start_date = date - timedelta(days=30)  # 向前查找30天
            schedule = calendar.schedule(start_date=start_date, end_date=date)
            if len(schedule) > 0:
                return schedule.index[-1].date()
            return None

    # 处理diff为负数的情况（获取前几个交易日）
    elif diff < 0:
        # 计算查找范围，保守估计每个交易日需要1.5个自然日
        start_date = date - timedelta(days=abs(diff) * 3)
        schedule = calendar.schedule(start_date=start_date, end_date=date)

        # 如果当前日期是交易日，则包含在内
        trading_days = schedule.index
        if date in trading_days.to_list():
            idx = trading_days.get_loc(date)
            target_idx = idx + diff  # diff是负数，所以是减去其绝对值
        else:
            # 当前日期不是交易日，找到小于当前日期的最后一个交易日
            trading_days = trading_days[trading_days < date]
            if len(trading_days) == 0:
                return None
            idx = len(trading_days) - 1
            target_idx = idx + diff + 1  # +1是因为我们已经排除了当前日期

        # 确保目标索引有效
        if target_idx < 0:
            # 如果目标索引超出范围，需要查找更早的日期
            earlier_start = start_date - timedelta(days=30)
            earlier_schedule = calendar.schedule(start_date=earlier_start, end_date=start_date)
            if len(earlier_schedule) >= abs(target_idx):
                return earlier_schedule.index[target_idx].date()
            return None

        return trading_days[target_idx].date()

    # 处理diff为正数的情况（获取后几个交易日）
    else:
        # 计算查找范围，保守估计每个交易日需要1.5个自然日
        end_date = date + timedelta(days=diff * 3)
        schedule = calendar.schedule(start_date=date, end_date=end_date)

        # 如果当前日期是交易日，则包含在内
        trading_days = schedule.index
        if date in trading_days:
            idx = trading_days.get_loc(date)
            target_idx = idx + diff
        else:
            # 当前日期不是交易日，找到大于当前日期的第一个交易日
            trading_days = trading_days[trading_days > date]
            if len(trading_days) == 0:
                return None
            idx = 0
            target_idx = idx + diff - 1  # -1是因为我们已经考虑了第一个大于当前日期的交易日

        # 确保目标索引有效
        if target_idx >= len(trading_days):
            # 如果目标索引超出范围，需要查找更晚的日期
            later_end = end_date + timedelta(days=30)
            later_schedule = calendar.schedule(start_date=end_date, end_date=later_end)
            if len(later_schedule) > 0:
                remaining = target_idx - len(trading_days)
                if remaining < len(later_schedule):
                    return later_schedule.index[remaining].date()
            return None

        return trading_days[target_idx].date()


def get_trading_days_between(start_date, end_date, calendar_name='HKEX'):
    """
    获取两个日期之间的交易日天数和交易日列表

    参数:
        start_date (str/datetime/Timestamp): 开始日期，包含在内
        end_date (str/datetime/Timestamp): 结束日期，包含在内
        calendar_name (str): 交易所日历名称，默认为香港交易所(HKEX)
                            可选值包括: 'NYSE', 'NASDAQ', 'SSE' (上海), 'SZSE' (深圳),
                            'HKEX' (香港), 'TSX' (多伦多), 'LSE' (伦敦) 等

    返回:
        tuple: (交易日天数, 交易日列表)
    """
    import pandas_market_calendars as mcal
    import pandas as pd

    # 确保日期是Timestamp类型
    start_date = pd.Timestamp(start_date)
    end_date = pd.Timestamp(end_date)

    # 获取交易所日历
    calendar = mcal.get_calendar(calendar_name)

    # 获取指定日期范围内的交易日
    schedule = calendar.schedule(start_date=start_date, end_date=end_date)

    # 提取交易日列表
    trading_days = schedule.index.date.tolist()

    # 返回交易日天数和交易日列表
    return len(trading_days), trading_days


def get_trading_days_count(start_date, end_date, calendar_name='HKEX'):
    """
    获取两个日期之间的交易日天数（简化版本）

    参数:
        start_date (str/datetime/Timestamp): 开始日期，包含在内
        end_date (str/datetime/Timestamp): 结束日期，包含在内
        calendar_name (str): 交易所日历名称，默认为香港交易所

    返回:
        int: 交易日天数
    """
    count, _ = get_trading_days_between(start_date, end_date, calendar_name)
    return count


def get_next_trading_day(date, calendar_name='HKEX', n=1):
    """
    获取指定日期后的第n个交易日

    参数:
        date (str/datetime/Timestamp): 指定日期
        calendar_name (str): 交易所日历名称，默认为香港交易所
        n (int): 向后第n个交易日，默认为1（下一个交易日）

    返回:
        datetime.date: 下一个交易日的日期
    """
    import pandas_market_calendars as mcal
    import pandas as pd
    from datetime import timedelta

    # 确保日期是Timestamp类型
    date = pd.Timestamp(date)

    # 获取交易所日历
    calendar = mcal.get_calendar(calendar_name)

    # 计算查找范围（保守估计每个交易日需要1.5个自然日）
    end_date = date + timedelta(days=int(n * 1.5) + 5)

    # 获取指定日期范围内的交易日
    schedule = calendar.schedule(start_date=date, end_date=end_date)

    # 提取交易日列表
    trading_days = schedule.index.date.tolist()

    # 如果当前日期是交易日，则从下一个交易日开始计数
    if date.date() in trading_days:
        start_idx = trading_days.index(date.date()) + 1
    else:
        # 如果当前日期不是交易日，则从下一个交易日开始计数
        future_days = [d for d in trading_days if d > date.date()]
        if not future_days:
            return None  # 没有找到未来的交易日
        start_idx = trading_days.index(future_days[0])

    # 计算目标交易日的索引
    target_idx = start_idx + n - 1

    # 确保索引在有效范围内
    if target_idx >= len(trading_days):
        return None  # 超出了查找范围

    return trading_days[target_idx]


def get_prev_trading_day(date, calendar_name='HKEX', n=1):
    """
    获取指定日期前的第n个交易日

    参数:
        date (str/datetime/Timestamp): 指定日期
        calendar_name (str): 交易所日历名称，默认为香港交易所
        n (int): 向前第n个交易日，默认为1（前一个交易日）

    返回:
        datetime.date: 前一个交易日的日期
    """
    import pandas_market_calendars as mcal
    import pandas as pd
    from datetime import timedelta

    # 确保日期是Timestamp类型
    date = pd.Timestamp(date)

    # 获取交易所日历
    calendar = mcal.get_calendar(calendar_name)

    # 计算查找范围（保守估计每个交易日需要1.5个自然日）
    start_date = date - timedelta(days=int(n * 1.5) + 5)

    # 获取指定日期范围内的交易日
    schedule = calendar.schedule(start_date=start_date, end_date=date)

    # 提取交易日列表
    trading_days = schedule.index.date.tolist()

    # 如果当前日期是交易日，则从当前交易日开始向前计数
    if date.date() in trading_days:
        end_idx = trading_days.index(date.date()) - 1
    else:
        # 如果当前日期不是交易日，则从前一个交易日开始计数
        past_days = [d for d in trading_days if d < date.date()]
        if not past_days:
            return None  # 没有找到过去的交易日
        end_idx = trading_days.index(past_days[-1])

    # 计算目标交易日的索引
    target_idx = end_idx - (n - 1)

    # 确保索引在有效范围内
    if target_idx < 0:
        return None  # 超出了查找范围

    return trading_days[target_idx]


def find_files_with_date_after(folder_path, file_regex, target_date):
    """
    查找指定路径下符合正则表达式且文件名中包含日期格式(如"10 Jun 2025")且日期大于目标日期的所有文件，
    并对这些文件执行指定操作。

    参数：
        folder_path (str): 要搜索的文件夹路径
        target_date (str): 目标日期，格式为'YYYY-MM-DD'，如'2025-06-09'
        file_regex (str, optional): 文件名匹配的正则表达式，可包含{target_date}占位符
                                   例如: '^Outstanding Transaction Report ST-FM-TX-005-01 DD {target_date}\\.xls$'
        file_operation (callable, optional): 对每个找到的文件执行的操作函数，
                                            接收文件路径作为参数

    返回：
        list: 找到的文件路径列表
    """
    import os
    import re
    from datetime import datetime

    # 将目标日期字符串转换为datetime对象
    target_date_obj = datetime.strptime(target_date, "%Y-%m-%d")

    # 定义日期格式的正则表达式模式
    # 匹配格式如"10 Jun 2025"、"10 June 2025"、"10-Jun-2025"等
    date_pattern = re.compile(r'(\d{1,2})[\s-]*(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec|January|February|March|April|May|June|July|August|September|October|November|December)[\s-]*(\d{4})', re.IGNORECASE)

    # 编译文件名正则表达式（如果提供）
    file_regex_pattern = None
    if file_regex:
        # 将正则表达式中的{target_date}替换为通配符
        # 例如：'^Outstanding Transaction Report ST-FM-TX-005-01 DD {target_date}\.xls$'
        # 变为：'^Outstanding Transaction Report ST-FM-TX-005-01 DD .*\.xls$'
        generic_regex = file_regex.replace("{target_date}", r".*")
        file_regex_pattern = re.compile(generic_regex, re.IGNORECASE)

    # 月份名称到数字的映射
    month_map = {
        'jan': 1, 'january': 1,
        'feb': 2, 'february': 2,
        'mar': 3, 'march': 3,
        'apr': 4, 'april': 4,
        'may': 5,
        'jun': 6, 'june': 6,
        'jul': 7, 'july': 7,
        'aug': 8, 'august': 8,
        'sep': 9, 'september': 9,
        'oct': 10, 'october': 10,
        'nov': 11, 'november': 11,
        'dec': 12, 'december': 12
    }

    matching_files = []

    # 遍历文件夹及其子文件夹
    for root, _, files in os.walk(folder_path):
        for filename in files:
            # 如果提供了文件名正则表达式，先检查文件名是否匹配
            if file_regex_pattern and not file_regex_pattern.match(filename):
                continue

            # 在文件名中查找日期
            match = date_pattern.search(filename)
            if match:
                day, month_name, year = match.groups()
                # 将月份名称转换为数字
                month = month_map.get(month_name.lower(), 0)
                if month > 0:
                    try:
                        # 构建文件中的日期对象
                        file_date_obj = datetime(int(year), month, int(day))

                        # 检查文件日期是否大于目标日期
                        if file_date_obj > target_date_obj:
                            file_path = os.path.join(root, filename)
                            matching_files.append(file_path)

                    except ValueError:
                        # 忽略无效日期
                        continue

    return matching_files


def find_files_with_date_after_1(folder_path, file_regex, target_date):
    """
    查找指定路径下符合正则表达式且文件名中包含日期格式(如"10 Jun 2025")且日期大于目标日期的所有文件，
    并对这些文件执行指定操作。

    参数：
        folder_path (str): 要搜索的文件夹路径
        target_date (str): 目标日期，格式为'YYYY-MM-DD'，如'2025-06-09'
        file_regex (str, optional): 文件名匹配的正则表达式，可包含{target_date}占位符
                                   例如: '^Outstanding Transaction Report ST-FM-TX-005-01 DD {target_date}\\.xls$'
        file_operation (callable, optional): 对每个找到的文件执行的操作函数，
                                            接收文件路径作为参数

    返回：
        list: 找到的文件路径列表
    """
    import os
    import re
    from datetime import datetime

    # 将目标日期字符串转换为datetime对象
    target_date_obj = datetime.strptime(target_date, "%Y-%m-%d")

    # 定义日期格式的正则表达式模式
    # 匹配格式如"10 Jun 2025"、"10 June 2025"、"10-Jun-2025"等
    date_pattern = re.compile(r'(\d{1,2})[\s-]*(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec|January|February|March|April|May|June|July|August|September|October|November|December)[\s-]*(\d{4})', re.IGNORECASE)

    # 编译文件名正则表达式（如果提供）
    file_regex_pattern = None
    if file_regex:
        # 将正则表达式中的{target_date}替换为通配符
        # 例如：'^Outstanding Transaction Report ST-FM-TX-005-01 DD {target_date}\.xls$'
        # 变为：'^Outstanding Transaction Report ST-FM-TX-005-01 DD .*\.xls$'
        generic_regex = file_regex.replace("{target_date}", r".*")
        file_regex_pattern = re.compile(generic_regex, re.IGNORECASE)

    # 月份名称到数字的映射
    month_map = {
        'jan': 1, 'january': 1,
        'feb': 2, 'february': 2,
        'mar': 3, 'march': 3,
        'apr': 4, 'april': 4,
        'may': 5,
        'jun': 6, 'june': 6,
        'jul': 7, 'july': 7,
        'aug': 8, 'august': 8,
        'sep': 9, 'september': 9,
        'oct': 10, 'october': 10,
        'nov': 11, 'november': 11,
        'dec': 12, 'december': 12
    }

    matching_files = []

    # 遍历文件夹及其子文件夹
    for root, _, files in os.walk(folder_path):
        for filename in files:
            # 如果提供了文件名正则表达式，先检查文件名是否匹配
            if file_regex_pattern and not file_regex_pattern.match(filename):
                continue

            # 在文件名中查找日期
            match = date_pattern.search(filename)
            if match:
                day, month_name, year = match.groups()
                # 将月份名称转换为数字
                month = month_map.get(month_name.lower(), 0)
                if month > 0:
                    try:
                        # 构建文件中的日期对象
                        file_date_obj = datetime(int(year), month, int(day))

                        # 检查文件日期是否大于目标日期
                        if file_date_obj > target_date_obj:
                            file_path = os.path.join(root, filename)
                            matching_files.append(file_path)

                    except ValueError:
                        # 忽略无效日期
                        continue

    return matching_files


def find_latest_file_before_date(folder_path, regex, target_date):
    """
    查找指定路径下符合正则表达式且文件名中的日期不大于目标日期的最新文件

    参数：
        folder_path (str): 要搜索的文件夹路径
        regex (str): 文件名匹配的正则表达式，可包含{target_date}占位符
        target_date (str): 目标日期，格式为'YYYYMMDD'

    返回：
        str: 找到的文件路径，如果没找到则返回None
    """
    # 将YYYYMMDD格式转换为datetime对象
    target_date_obj = dt.datetime.strptime(target_date, "%Y%m%d")

    # 将正则表达式中的日期占位符替换为通配符
    # 例如：^H0ZOFI_SR1000C01A_{target_date}_1_164009\.CSV$
    # 变为：^H0ZOFI_SR1000C01A_\d{8}_1_164009\.CSV$
    generic_regex = regex.replace("{target_date}", r"\d{8}")
    pattern = re.compile(generic_regex, re.IGNORECASE)

    matched_files = []

    # 搜索所有匹配的文件
    for root, dirs, files in os.walk(folder_path):
        for filename in files:
            if pattern.match(filename):
                # 从文件名中提取日期
                file_date_str = extract_date(filename)
                if file_date_str:
                    file_date_obj = dt.datetime.strptime(file_date_str, "%Y%m%d")
                    # 只保留日期不大于目标日期的文件
                    if file_date_obj <= target_date_obj:
                        file_path = os.path.join(root, filename)
                        matched_files.append((file_date_obj, file_path))

    if not matched_files:
        return None

    # 按日期降序排序，取第一个（最新的）
    matched_files.sort(key=lambda x: x[0], reverse=True)
    return matched_files[0][1]  # 返回文件路径


def find_earliest_file_after_date(folder_path, regex, target_date):
    """
    查找指定路径下符合正则表达式且文件名中的日期大于目标日期的最早文件

    参数：
        folder_path (str): 要搜索的文件夹路径
        regex (str): 文件名匹配的正则表达式，可包含{target_date}占位符
        target_date (str): 目标日期，格式为'YYYYMMDD'

    返回：
        str: 找到的文件路径，如果没找到则返回None
    """
    # 将YYYYMMDD格式转换为datetime对象
    target_date_obj = dt.datetime.strptime(target_date, "%Y%m%d")

    # 将正则表达式中的日期占位符替换为通配符
    generic_regex = regex.replace("{target_date}", r"\d{8}")
    pattern = re.compile(generic_regex, re.IGNORECASE)

    matched_files = []

    # 搜索所有匹配的文件
    for root, dirs, files in os.walk(folder_path):
        for filename in files:
            if pattern.match(filename):
                # 从文件名中提取日期
                file_date_str = extract_date(filename)
                if file_date_str:
                    file_date_obj = dt.datetime.strptime(file_date_str, "%Y%m%d")
                    # 只保留日期大于目标日期的文件
                    if file_date_obj > target_date_obj:
                        file_path = os.path.join(root, filename)
                        matched_files.append((file_date_obj, file_path))

    if not matched_files:
        return None

    # 按日期升序排序，取第一个（最早的）
    matched_files.sort(key=lambda x: x[0])
    return matched_files[0][1]  # 返回文件路径


def find_latest_file(folder_path, regex):
    pattern = re.compile(regex.lower())
    latest_time = 0
    latest_file = None
    matched_files = []

    for root, dirs, files in os.walk(folder_path):
        for filename in files:
            if pattern.match(filename.lower()):
                file_path = os.path.join(root, filename)
                matched_files.append(filename)
                # timestamp = os.path.getmtime(file_path)
                # if timestamp > latest_time:
                #     latest_time = timestamp
                #     latest_file = file_path

    if not matched_files:
        return None

    matched_files.sort(reverse=True)
    max_filename = matched_files[0]

    # return latest_file
    return max_filename


def find_specific_files_by_date(root_dir, regex, target_date):
    """
    根据动态参数查找特定格式的CSV文件

    参数：
        root_dir (str)  : 要搜索的根目录路径
        target_date (str): 要匹配的8位日期（默认：20250228）
        components (tuple): 要匹配的组件编号（默认：5和6，对应C05/C06）

    返回：
        list: 匹配文件的完整路径列表
    """

    regex = regex.format(target_date=target_date)

    # 参数有效性校验
    if not re.match(r"^\d{8}$", target_date):
        raise ValueError("日期格式必须为8位数字（YYYYMMDD）")

    # 构建动态正则表达式
    pattern = re.compile(
        regex,      # 6位数字+.CSV
        re.IGNORECASE
    )

    matched_files = []

    # 执行文件搜索
    for dirpath, _, filenames in os.walk(root_dir):
        for name in filenames:
            if pattern.match(name):
                matched_files.append(os.path.join(dirpath, name))

    if not matched_files:
        return None

    return matched_files


def find_specific_files_by_creation_date(root_dir, regex, target_date):
    """
    根据文件名正则表达式和文件创建日期查找文件

    参数：
        root_dir (str)  : 要搜索的根目录路径
        regex (str)     : 文件名匹配的正则表达式（可包含{target_date}占位符）
        target_date (str): 要匹配的8位日期（YYYYMMDD格式）

    返回：
        list: 匹配文件的完整路径列表，如果没有找到则返回None
    """
    import os
    import re
    import datetime
    from pathlib import Path

    # 参数有效性校验
    if not re.match(r"^\d{8}$", target_date):
        raise ValueError("日期格式必须为8位数字（YYYYMMDD）")

    # 将target_date转换为datetime对象用于比较
    try:
        target_datetime = datetime.datetime.strptime(target_date, "%Y%m%d")
        target_date_only = target_datetime.date()
    except ValueError:
        raise ValueError(f"无效的日期格式: {target_date}")

    # 处理正则表达式中的占位符
    # processed_regex = regex.format(target_date=target_date)

    # 构建正则表达式模式
    pattern = re.compile(regex, re.IGNORECASE)

    matched_files = []

    # 执行文件搜索
    for dirpath, _, filenames in os.walk(root_dir):
        for name in filenames:
            # 首先检查文件名是否匹配正则表达式
            if pattern.match(name):
                file_path = os.path.join(dirpath, name)

                try:
                    # 获取文件创建时间
                    file_stat = os.stat(file_path)
                    # 在Windows上使用st_ctime，在Unix上使用st_birthtime（如果可用）
                    if hasattr(file_stat, 'st_birthtime'):
                        # macOS/BSD系统
                        creation_time = file_stat.st_birthtime
                    else:
                        # Windows和Linux系统
                        creation_time = file_stat.st_ctime

                    # 转换为日期对象
                    file_creation_date = datetime.datetime.fromtimestamp(creation_time).date()

                    # 检查创建日期是否匹配
                    if file_creation_date == target_date_only:
                        matched_files.append(file_path)

                except (OSError, IOError) as e:
                    # 如果无法获取文件信息，跳过该文件
                    print(f"警告: 无法获取文件 {file_path} 的创建时间: {e}")
                    continue

    if not matched_files:
        return None

    return matched_files


def find_files_by_date_advanced(root_dir, regex, target_date, date_type='creation', tolerance_days=0):
    """
    高级版本：根据文件名正则表达式和文件日期查找文件

    参数：
        root_dir (str)      : 要搜索的根目录路径
        regex (str)         : 文件名匹配的正则表达式
        target_date (str)   : 要匹配的8位日期（YYYYMMDD格式）
        date_type (str)     : 日期类型 ('creation', 'modified', 'accessed')
        tolerance_days (int): 日期容差天数（0表示精确匹配）

    返回：
        list: 匹配文件信息的字典列表，包含路径、创建时间、修改时间等
    """
    import os
    import re
    import datetime
    from pathlib import Path

    # 参数验证
    if not re.match(r"^\d{8}$", target_date):
        raise ValueError("日期格式必须为8位数字（YYYYMMDD）")

    if date_type not in ['creation', 'modified', 'accessed']:
        raise ValueError("date_type必须是'creation', 'modified', 或'accessed'")

    if tolerance_days < 0:
        raise ValueError("tolerance_days必须为非负整数")

    # 转换目标日期
    try:
        target_datetime = datetime.datetime.strptime(target_date, "%Y%m%d")
        target_date_only = target_datetime.date()
    except ValueError:
        raise ValueError(f"无效的日期格式: {target_date}")

    # 处理正则表达式
    processed_regex = regex.format(target_date=target_date)
    pattern = re.compile(processed_regex, re.IGNORECASE)

    matched_files = []

    # 搜索文件
    for dirpath, _, filenames in os.walk(root_dir):
        for name in filenames:
            if pattern.match(name):
                file_path = os.path.join(dirpath, name)

                try:
                    file_stat = os.stat(file_path)

                    # 根据date_type选择相应的时间戳
                    if date_type == 'creation':
                        if hasattr(file_stat, 'st_birthtime'):
                            timestamp = file_stat.st_birthtime
                        else:
                            timestamp = file_stat.st_ctime
                    elif date_type == 'modified':
                        timestamp = file_stat.st_mtime
                    else:  # accessed
                        timestamp = file_stat.st_atime

                    file_date = datetime.datetime.fromtimestamp(timestamp).date()

                    # 检查日期是否在容差范围内
                    date_diff = abs((file_date - target_date_only).days)
                    if date_diff <= tolerance_days:
                        file_info = {
                            'path': file_path,
                            'name': name,
                            'creation_date': datetime.datetime.fromtimestamp(file_stat.st_ctime).date(),
                            'modified_date': datetime.datetime.fromtimestamp(file_stat.st_mtime).date(),
                            'accessed_date': datetime.datetime.fromtimestamp(file_stat.st_atime).date(),
                            'size': file_stat.st_size,
                            'date_diff': date_diff
                        }
                        matched_files.append(file_info)

                except (OSError, IOError) as e:
                    print(f"警告: 无法获取文件 {file_path} 的信息: {e}")
                    continue

    # 按日期差异排序
    matched_files.sort(key=lambda x: x['date_diff'])

    return matched_files if matched_files else None


def find_specific_files_by_name_and_date(root_dir, regex, fund_name, target_date):
    """
    根据动态参数查找特定格式的CSV文件

    参数：
        root_dir (str)  : 要搜索的根目录路径
        target_date (str): 要匹配的8位日期（默认：20250228）
        components (tuple): 要匹配的组件编号（默认：5和6，对应C05/C06）

    返回：
        list: 匹配文件的完整路径列表
    """

    regex = regex.format(fund_name=fund_name, target_date=target_date)

    # 参数有效性校验
    if not re.match(r"^\d{8}$", target_date):
        raise ValueError("日期格式必须为8位数字（YYYYMMDD）")

    # 构建动态正则表达式
    pattern = re.compile(
        regex,      # 6位数字+.CSV
        re.IGNORECASE
    )

    matched_files = []

    # 执行文件搜索
    for dirpath, _, filenames in os.walk(root_dir):
        for name in filenames:
            if pattern.match(name):
                matched_files.append(os.path.join(dirpath, name))

    if not matched_files:
        return None

    return matched_files[0]


def find_file_by_date(root_dir, regex, target_date):
    """
    根据YYYYMMDD日期查找 J-Tiger_NAV_Check {日期}.xlsx 文件

    参数：
        root_dir : 要搜索的根目录
        date_yyyymmdd : 日期参数（格式：YYYYMMDD）
        日期会被转换为28 Feb 2025
    返回：
        list: 匹配文件的完整路径列表
    """

    # 验证日期格式
    # try:
    #     date_obj = datetime.strptime(target_date, "%Y%m%d")
    # except ValueError:
    #     raise ValueError("日期格式必须为YYYYMMDD（如20250228）")
    #
    # # 转换日期格式：YYYYMMDD → 28 Feb 2025
    # formatted_date = date_obj.strftime("%d %b %Y")  # %b生成月份缩写（如Feb）

    regex = regex.format(target_date=target_date)

    # 转义特殊字符并编译正则
    pattern = re.compile(
        regex,
        re.IGNORECASE  # 忽略文件名大小写
    )
    # 递归搜索文件
    matched_files = []
    for dirpath, _, filenames in os.walk(root_dir):
        for filename in filenames:
            if pattern.match(filename):
                matched_files.append(os.path.join(dirpath, filename))

    if not matched_files:
        return None

    return matched_files[0]


def extract_date(s):
    # 匹配字符串中所有8位连续数字（如20250307）
    possible_dates = re.findall(r'\d{8}', s)
    for date_str in possible_dates:
        try:
            # 验证是否为合法日期（格式YYYYMMDD）
            datetime.strptime(date_str, "%Y%m%d")
            return date_str  # 返回第一个有效日期
        except ValueError:
            continue
    return None


def number_to_excel_column(n: int) -> str:
    """将数字转换为Excel列字母（支持任意正整数）"""
    if n <= 0:
        raise ValueError("输入数字必须为正整数")

    letters = []
    while n > 0:
        n, remainder = divmod(n - 1, 26)  # n-1确保余数从0开始
        letters.append(chr(65 + remainder))  # 65是'A'的ASCII码
    return ''.join(reversed(letters))  # 逆序拼接字母


def excel_column_to_number(column: str) -> int:
    """将Excel列字母转换为数字"""
    column = column.upper().strip()
    num = 0
    for char in column:
        if not char.isalpha():
            raise ValueError("输入必须为字母")
        num = num * 26 + (ord(char) - 64)  # 65-64=1
    return num


def replace_chart_with_image(sheet, chart_name, temp_image_path="temp_chart.png"):
    # 获取原图表对象
    chart = sheet.charts[chart_name]

    # 获取原图表的位置和尺寸
    left = chart.left
    top = chart.top
    width = chart.width
    height = chart.height

    # 导出图表为图片
    chart.to_png(temp_image_path)  # 调用Excel底层API

    # 删除原图表
    chart.delete()

    # 插入图片到原位置
    pic = sheet.pictures.add(temp_image_path,
                             left=left,
                             top=top,
                             width=width,
                             height=height)

    # 删除临时图片文件
    os.remove(temp_image_path)
    return pic


def find_prev_trading_day_by_month(df, current_date, months=1):
    df['nav_date'] = pd.to_datetime(df['nav_date'])
    df = df.set_index('nav_date').sort_index()
    # 计算目标日期（当前日期回退指定月数）
    target_date = pd.to_datetime(current_date) - pd.offsets.MonthEnd(months)
    # 获取目标日期前的最后一个有效交易日
    prev_trading_day = df.index.asof(target_date)
    return prev_trading_day


def find_nearest_trading_day_by_date(df, col_name, target_date):
    df[col_name] = pd.to_datetime(df[col_name])
    df = df.set_index(col_name).sort_index()
    target_date = pd.to_datetime(target_date)
    # 确保索引是 datetime 类型
    df.index = pd.to_datetime(df.index)
    # 找到小于或等于目标日期的最近日期
    nearest_date = df.index[df.index <= target_date].max()
    if pd.isna(nearest_date):
        # df = df.reset_index()
        # nearest_date = df.iloc[0]['nav_date']
        return None, None
    nearest_date_index = len(df.index[df.index <= target_date]) - 1
    return nearest_date, nearest_date_index


def calculate_return_by_month(df, current_date, months=1):
    prev_date = find_prev_trading_day_by_month(df, current_date, months)
    current_date = find_nearest_trading_day_by_date(df, current_date)
    if pd.isna(current_date):  # 处理无历史数据的情况
        return '-'
    df['nav_date'] = pd.to_datetime(df['nav_date'])
    df = df.set_index('nav_date').sort_index()
    if pd.isna(prev_date):  # 处理无历史数据的情况
        return '-'
    else:
        prev_price = df.loc[prev_date, 'nav_price']
    current_price = df.loc[current_date, 'nav_price']
    rtn_rate = (current_price - prev_price) / prev_price
    if rtn_rate == 0:
        return '-'
    else:
        return rtn_rate

def calculate_return_by_date(df, current_date, target_date):
    current_date = find_nearest_trading_day_by_date(df, current_date)
    if pd.isna(current_date):  # 处理无历史数据的情况
        return '-'
    prev_date = find_nearest_trading_day_by_date(df, target_date)
    if pd.isna(prev_date):
        prev_date = df.iloc[0]['nav_date']
    df['nav_date'] = pd.to_datetime(df['nav_date'])
    df = df.set_index('nav_date').sort_index()
    prev_price = df.loc[prev_date, 'nav_price']
    current_price = df.loc[current_date, 'nav_price']
    return (current_price - prev_price) / prev_price  # 简单收益率公式[3](@ref)


def calculate_return_by_calendar_year(df, current_date, target_date):
    final_date = df.iloc[-1]['nav_date']
    current_date = find_nearest_trading_day_by_date(df, current_date)
    if pd.isna(current_date):  # 处理无历史数据的情况
        return '-'
    inception_date = df.iloc[0]['nav_date']
    if final_date - inception_date <= pd.Timedelta(days=365):
        return '-'
    prev_date = find_nearest_trading_day_by_date(df, target_date)
    if pd.isna(prev_date):
        prev_date = df.iloc[0]['nav_date']
    df['nav_date'] = pd.to_datetime(df['nav_date'])
    df = df.set_index('nav_date').sort_index()
    prev_price = df.loc[prev_date, 'nav_price']
    current_price = df.loc[current_date, 'nav_price']
    return (current_price - prev_price) / prev_price  # 简单收益率公式[3](@ref)


def get_max_row(sheet, start_row, total_cols):
    # 初始化变量
    longest_col = None  # 最长列的列号
    longest_length = 0  # 最长列的长度
    last_row_of_longest_col = None  # 最长列的最后一行行号

    # 遍历所有列
    for col in range(1, total_cols + 1):
        current_length = 0  # 当前列的长度
        last_row_of_current_col = start_row - 1  # 当前列的最后一行行号

        # 使用 while True 循环逐行检查
        row = start_row
        while True:
            cell_value = sheet.range(row, col).value
            if cell_value is not None and cell_value != "":
                current_length += 1
                last_row_of_current_col = row  # 更新当前列的最后一行行号
                row += 1
            else:
                break  # 遇到空单元格时停止

        # 更新最长列的信息
        if current_length > longest_length:
            longest_length = current_length
            longest_col = col
            last_row_of_longest_col = last_row_of_current_col  # 保存最长列的最后一行行号
    return last_row_of_longest_col


def get_max_col_in_row(sheet, which_row, start_col):
    last_col = None
    col_num = start_col

    # 使用 while 循环逐列检查第 5 行的值
    while True:
        cell_value = sheet.range(which_row, col_num).value  # 获取第 5 行第 col_num 列的值
        if cell_value is None or cell_value == "":  # 如果单元格为空
            last_col = col_num - 1  # 记录前一列的列号
            break
        col_num += 1  # 移动到下一列

    return last_col


def clear_merged_range_contents(sheet, range_address):
    """
    安全清除包含合并单元格的区域内容（保留合并格式）
    """
    # 获取目标区域
    target_range = sheet.range(range_address)

    # 遍历范围内的每个单元格
    for cell in target_range:
        # 如果单元格是合并区域的一部分，操作合并区域的主单元格
        if cell.merge_cells:
            cell.merge_area.clear_contents()
        else:
            cell.clear_contents()


# 创建 ConfigParser 对象
config = configparser.ConfigParser()
# 读取配置文件
try:
    config.read('config.ini', encoding='utf-8')
except FileNotFoundError:
    print("错误：未找到 config.ini 文件！")
    exit(1)

config_cat = 'base_config'
db_server = config.get(config_cat, 'db_server')
db_type = config.get(config_cat, 'db_type')



# db_fmsdb = engine.URL.create(
#     drivername='mssql',
#     username='fms_admin',
#     password=DesDecrypt("JEQygoEP3kNi4gXNGz5ydQ==").decode('utf-8'),
#     host=db_server,
#     port='1433',
#     database='FMSDB',
#     query={'driver': "ODBC Driver 11 for SQL Server"}
# )
# mssql_engine = create_engine(db_fmsdb, echo=False, fast_executemany=True, use_setinputsizes=False)

# db_marketdb = engine.URL.create(
#     drivername='mssql',
#     username='marketdb-reader',
#     password=DesDecrypt("1NKyHYixq9WjTbhIxyAqDg==").decode('utf-8'),
#     host=db_server,
#     port='1433',
#     database='MarketDB',
#     query={'driver': "ODBC Driver 11 for SQL Server"}
# )


class MSSQL_DBHandler:
    def __init__(self, connection_string):
        """
        初始化SQL Server操作类。

        :param connection_string: SQLAlchemy支持的连接字符串，例如：
                                  "mssql+pyodbc://username:password@server/database?driver=ODBC+Driver+17+for+SQL+Server"
        """
        self.connection_string = connection_string
        connection_string = engine.URL.create(
            drivername='mssql',
            username='fms_admin',
            password=DesDecrypt("JEQygoEP3kNi4gXNGz5ydQ==").decode('utf-8'),
            host=db_server,
            port='1433',
            database='FMSDB',
            query={'driver': "ODBC Driver 11 for SQL Server"}
        )
        self.engine = create_engine(connection_string, echo=False, fast_executemany=True, use_setinputsizes=False)

    def db_engine(self):
        return self.engine

    def query(self, sql_query, params=None):
        """
        执行SQL查询并返回结果为DataFrame。

        :param sql_query: 查询SQL语句。
        :param params: 可选的查询参数，字典形式。
        :return: 查询结果，pandas.DataFrame
        """
        try:
            with self.engine.connect() as connection:
                df = pd.read_sql_query(text(sql_query), connection, params=params)
                # print(df)
                # sys.stdout.flush()
            return df
        except Exception as e:
            print(f"查询失败: {e}")
            return None

    def execute_sql(self, sql_statement, params=None):
        """
        执行任意SQL语句（包括插入、更新、删除等）。

        :param sql_statement: 完整的SQL语句。
        :param params: 可选的SQL参数，字典形式。
        """
        try:
            with self.engine.begin() as connection:
                connection.execute(text(sql_statement), params or {})
            # print("SQL语句执行成功！")
        except Exception as e:
            print(f"SQL语句执行失败: {e}")


class SQLITE3_DBHandler:
    def __init__(self):
        """
        初始化SQL Server操作类。

        :param connection_string
        """
        db_path = 'fund_db.db'
        connection_string = 'sqlite:///' + db_path
        self.engine = create_engine(connection_string, echo=False)

    def db_engine(self):
        return self.engine

    def query(self, sql_query, params=None):
        """
        执行SQL查询并返回结果为DataFrame。

        :param sql_query: 查询SQL语句。
        :param params: 可选的查询参数，字典形式。
        :return: 查询结果，pandas.DataFrame
        """
        try:
            with self.engine.connect() as connection:
                df = pd.read_sql_query(text(sql_query), connection, params=params)
                # print(df)
                # sys.stdout.flush()
            return df
        except Exception as e:
            print(f"查询失败: {e}")
            return None

    def execute_sql(self, sql_statement, params=None):
        """
        执行任意SQL语句（包括插入、更新、删除等）。

        :param sql_statement: 完整的SQL语句。
        :param params: 可选的SQL参数，字典形式。
        """
        try:
            with self.engine.begin() as connection:
                connection.execute(text(sql_statement), params or {})
            # print("SQL语句执行成功！")
        except Exception as e:
            print(f"SQL语句执行失败: {e}")


class MyTimer(object):
    def __init__(self, start_time, interval, callback_proc, args=None, kwargs=None):
        self.__timer = None
        self.__start_time = start_time
        self.__interval = interval
        self.__callback_pro = callback_proc
        self.__args = args if args is not None else []
        self.__kwargs = kwargs if kwargs is not None else {}

    def exec_callback(self, args=None, kwargs=None):
        self.__callback_pro(*self.__args, **self.__kwargs)
        self.__timer = Timer(self.__interval, self.exec_callback)
        self.__timer.start()

    def start(self):
        # interval = self.__interval - (datetime.now().timestamp() - self.__start_time.timestamp())
        interval = self.__start_time.timestamp() - datetime.now().timestamp()
        print(interval)
        if interval < 0:
            print("目标开始时间小于当前时间，请重新设置！")
            sys.exit(0)
        self.__timer = Timer(interval, self.exec_callback)
        self.__timer.start()

    def cancel(self):
        self.__timer.cancel()
        self.__timer = None


class Utility(object):
    def num_to_char(self, num):
        """数字转中文"""
        num = str(num)
        new_str = ""
        num_dict = {"0": u"零", "1": u"一", "2": u"二", "3": u"三", "4": u"四", "5": u"五", "6": u"六", "7": u"七", "8": u"八", "9": u"九"}
        list_num = list(num)
        # print(list_num)
        shu = []
        for i in list_num:
            # print(num_dict[i])
            shu.append(num_dict[i])
        new_str = "".join(shu)
        # print(new_str)
        return new_str

    def to_long_date(self, date_str):
        return dt.datetime.strptime(date_str, "%Y%m%d").strftime('%Y-%m-%d')

    def to_short_date(self, date_str):
        return dt.datetime.strptime(date_str, "%Y-%m-%d").strftime('%Y%m%d')

    def resample_weekly(self, df, trading_days):
        df = df.set_index('trade_date')

        # 按周重采样（这里先按每天重采样成日线数据）
        df_resampled = df.resample('D').last()

        last_row_no = len(df_resampled) - 1

        trading_days['trade_date'] = pd.to_datetime(trading_days['trade_date'], format='%Y-%m-%d')
        trading_days['trade_date'] = trading_days['trade_date'].apply(lambda x: x.strftime('%Y-%m-%d'))

        trading_days_list = trading_days.values

        last_trading_day = None
        df_resampled_list = []

        index_no = 0

        for timestamp, row in df_resampled.iterrows():
            # 不是交易日,但是在nav_df中存在(因为nav_df做了日扩展)
            if str(timestamp.date()) not in trading_days_list:
                if timestamp.weekday() == 4 and last_trading_day is not None:
                    df_resampled_list.append(df_resampled.loc[last_trading_day])
                    last_trading_day = None

            else:  # 是交易日
                # 如果是当前周的最后一个交易日，则将当前行保存到df_resampled_list中，
                # 否则把current_date赋值为当前行的索引
                if timestamp.weekday() == 4:
                    df_resampled_list.append(row)
                else:  # 记录非周五的时间序号
                    if index_no == last_row_no:
                        df_resampled_list.append(row)
                    else:
                        last_trading_day = timestamp
            index_no += 1

        # 合并保存在df_resampled_list中的重采样数据
        df_resampled = pd.concat(df_resampled_list, axis=1).T
        df_resampled.reset_index(inplace=True)
        df_resampled.rename(columns={'index': 'trade_date'}, inplace=True)

        return df_resampled

    def coordinate_to_number(coordinate):
        letter = coordinate[:-1]  # 提取字母部分
        row = int(coordinate[-1])  # 提取数字部分

        column = 0
        for char in letter:
            column = column * 26 + (ord(char.upper()) - 64)  # 转换字母为列数

        return column, row

    def excel_to_pdf(self, excel_file_name, saved_pdf_file_name):
        # xlApp = Dispatch("Excel.Application")
        # xlApp.Visible = False
        # xlApp.DisplayAlerts = 0
        # wb = xlApp.Workbooks.Open(excel_file_name, False)
        # sheet_list = ['Sheet1']  # say you want to print these sheets
        # wb.WorkSheets(sheet_list).Select()
        # wb.ActiveSheet.ExportAsFixedFormat(0, saved_pdf_file_name)
        # pyperclip.copy('')
        # wb.Close(False)
        # wb.Close(False)
        # xlApp.Quit()
        # xlApp = None
        wb = xlw.Book(excel_file_name)
        sheet = wb.sheets['Sheet1']  # 获取指定的工作表对象
        try:
            sheet.api.ExportAsFixedFormat(0, saved_pdf_file_name)  # 打印指定的工作表
        except Exception as e:
            print("Error:", str(e))
        finally:
            wb.close()

