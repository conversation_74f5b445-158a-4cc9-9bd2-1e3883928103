from PublicFunc import *


if __name__ == '__main__':
    mail_host = config.get('base_config', 'mail_host')
    mail_port = config.get('base_config', 'mail_port')
    mail_user = config.get('base_config', 'mail_user')
    mail_password = config.get('base_config', 'mail_password')
    mail_auth_info = {'server': mail_host, 'user': mail_user, 'password': mail_password}
    mail_sender = config.get('base_config', 'mail_sender')
    mail_receivers = config.get('base_config', 'mail_receivers')
    mail_receivers = [item.strip() for item in mail_receivers.split(",")]
    bark_key = config.get('base_config', 'bark_key')

    hq_aum_file_regex = config.get('base_config', 'hq_aum_file_regex')
    blg_aum_file_regex = config.get('base_config', 'blg_aum_file_regex')
    aum_report_path = config.get('base_config', 'aum_report_path')
    fund_code_mapping_file = config.get('base_config', 'fund_code_mapping_file')

    fund_code_mapping_df = pd.read_excel(fund_code_mapping_file)
    fund_code_mapping_df['account_code'] = fund_code_mapping_df['account_code'].astype(str)
    fund_code_mapping_df['account_group_code'] = fund_code_mapping_df['account_group_code'].astype(str)

    current_date = dt.datetime.now()
    target_date = get_trading_day(current_date.strftime('%Y-%m-%d'), diff=-2)
    hq_aum_files = find_specific_files_by_date(aum_report_path, hq_aum_file_regex, target_date.strftime('%Y%m%d'))

    if hq_aum_files is None:
        print("No headquarter AUM report found for today, skip the process...")
        send2bark(bark_key, "AUM Automation", "No headquarter AUM report found for today, skip the process...")
        exit(0)

    for file in hq_aum_files:
        app = xlw.App(visible=False)
        wb = app.books.open(file)
        ws = wb.sheets[0]
        table_title = ws.range("A1").value
        fund_code = table_title[0:6]
        fund_name = fund_code_mapping_df.loc[fund_code_mapping_df['account_code'] == fund_code, 'account_name'].values[0]

        valuation_df = ws.range("A5").expand().options(pd.DataFrame, index=False).value
        # 确保关闭工作簿
        wb.close()
        fund_aum = valuation_df.loc[valuation_df['科目代码'] == '资产合计', 11].values[0]


