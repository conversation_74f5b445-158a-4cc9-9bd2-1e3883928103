from PublicFunc import *

if __name__ == '__main__':
    db = SQLITE3_DBHandler()

    mail_host = config.get('base_config', 'mail_host')
    mail_port = config.get('base_config', 'mail_port')
    mail_user = config.get('base_config', 'mail_user')
    mail_password = config.get('base_config', 'mail_password')
    mail_auth_info = {'server': mail_host, 'user': mail_user, 'password': mail_password}
    mail_sender = config.get('base_config', 'mail_sender')
    mail_receivers = config.get('base_config', 'mail_receivers')
    mail_receivers = [item.strip() for item in mail_receivers.split(",")]
    bark_key = config.get('base_config', 'bark_key')

    hq_multi_aum_file_regex = config.get('base_config', 'hq_multi_aum_file_regex')
    hq_single_aum_file_regex = config.get('base_config', 'hq_single_aum_file_regex')
    blg_aum_file_regex = config.get('base_config', 'blg_aum_file_regex')
    aum_report_path = config.get('base_config', 'aum_report_path')
    account_code_mapping_file = config.get('base_config', 'account_code_mapping_file')

    fx_rate = float(config.getfloat('base_config', 'fx_rate'))

    account_code_mapping_df = pd.read_excel(account_code_mapping_file)
    account_code_mapping_df['account_code'] = account_code_mapping_df['account_code'].astype(str)
    account_code_mapping_df['account_group_code'] = account_code_mapping_df['account_group_code'].astype(str)
    account_code_list = account_code_mapping_df['account_code'].tolist()
    account_code_list = ','.join(account_code_list)

    current_date = dt.datetime.now()
    target_date = get_trading_day(current_date.strftime('%Y-%m-%d'), diff=-1)
    aum_list = []

    warning_msg = ''

    # 处理上海母公司发来的AUM文件(N个产品N个文件)----------------------------------------------
    print("Processing Headquarter Multi AUM report...")
    hq_multi_aum_files = find_specific_files_by_date(aum_report_path, hq_multi_aum_file_regex, target_date.strftime('%Y%m%d'))

    if hq_multi_aum_files is None:
        print("No Headquarter Multi AUM report found for today, skip the process...")
        send2bark(bark_key, "AUM Automation", "No Headquarter Multi AUM report found for today, skip the process...")
        exit(0)

    temp_account_code_mapping_df = account_code_mapping_df[account_code_mapping_df['code_type'] == 'hq_multi'].reset_index(drop=True)

    for file in hq_multi_aum_files:
        app = xlw.App(visible=False)
        wb = app.books.open(file)
        ws = wb.sheets[0]
        table_title = ws.range("A1").value
        account_code = table_title[0:6]
        print("Processing account code: " + account_code)
        account_group_code = temp_account_code_mapping_df.loc[temp_account_code_mapping_df['account_code'] == account_code, 'account_group_code'].values[0]
        account_name = temp_account_code_mapping_df.loc[temp_account_code_mapping_df['account_code'] == account_code, 'account_name'].values[0]

        valuation_df = ws.range("A8").expand().options(pd.DataFrame, header=False, index=False).value
        # 确保关闭工作簿
        wb.close()
        fund_aum = valuation_df.loc[valuation_df.iloc[:, 0] == '资产合计', 11].values[0]
        aum_list.append([target_date, account_group_code, account_code, account_name, fund_aum, fund_aum / fx_rate])

    if len(aum_list) != len(temp_account_code_mapping_df):
        expected_codes = temp_account_code_mapping_df['account_code'].tolist()
        actual_codes = [item[0] for item in aum_list]
        missing_codes = list(set(expected_codes) - set(actual_codes))

        if missing_codes:
            warning_msg = f"Headquarter Multi AUM数据不完整:<br>"
            warning_msg += f"预期: {len(expected_codes)} 个账户<br>"
            warning_msg += f"实际: {len(actual_codes)} 个账户<br>"
            warning_msg += f"缺失账户代码: {', '.join(sorted(missing_codes))}<br>"
            warning_msg += f"（缺失账户AUM使用上一交易日的数据）<br>"

            # 获取缺失账户的名称
            for account_code in missing_codes:
                print("Processing account code: " + account_code)
                account_name = temp_account_code_mapping_df.loc[
                    temp_account_code_mapping_df['account_code'] == account_code,
                    'account_name'
                ].values[0]
                account_group_code = temp_account_code_mapping_df.loc[
                    temp_account_code_mapping_df['account_code'] == account_code,
                    'account_group_code'
                ].values[0]
                sql = "select aum_original, aum_local from aum_record where account_code = '{account_code}' and aum_date = '{get_trading_day(target_date.strftime('%Y-%m-%d'), diff=-1).strftime('%Y-%m-%d')}'".format(account_code=account_code, target_date=target_date)
                result = db.execute_sql(sql)
                if result:
                    aum_original, aum_local = result[0]
                    aum_list.append([target_date, account_group_code, account_code, account_name, aum_original, aum_local])
                else:
                    aum_list.append([target_date, account_group_code, account_code, account_name, 0, 0])

    # 处理 bloomberg 发来的AUM文件----------------------------------------------------
    print("Processing Bloomberg AUM report...")
    blg_aum_files = find_specific_files_by_creation_date(aum_report_path, blg_aum_file_regex, current_date.strftime('%Y%m%d'))

    if blg_aum_files is None:
        print("No bloomberg AUM report found for today, skip the process...")
        send2bark(bark_key, "AUM Automation", "No bloomberg AUM report found for today, skip the process...")
        exit(0)

    temp_account_code_mapping_df = account_code_mapping_df[account_code_mapping_df['code_type'] == 'bloomberg'].reset_index(drop=True)

    for file in blg_aum_files:
        app = xlw.App(visible=False)
        wb = app.books.open(file)
        ws = wb.sheets[0]
        blg_aum_date = pd.to_datetime(ws.range("B4").value)
        if blg_aum_date.strftime('%Y-%m-%d') != target_date.strftime('%Y-%m-%d'):
            print(f"Warning: Bloomberg AUM date {blg_aum_date.strftime('%Y-%m-%d')} is not the same as target date {target_date.strftime('%Y-%m-%d')}, skip the process...")
            send2bark(bark_key, "AUM Automation", f"Warning: Bloomberg AUM date {blg_aum_date.strftime('%Y-%m-%d')} is not the same as target date {target_date.strftime('%Y-%m-%d')}, skip the process...")
            exit(0)
            # continue

        valuation_df = ws.range("A7").expand().options(pd.DataFrame, header=True, index=False).value
        # 确保关闭工作簿
        wb.close()

        for index, row in temp_account_code_mapping_df.iterrows():
            account_code = row['account_code']
            print("Processing account code: " + account_code)
            account_group_code = row['account_group_code']
            account_name = row['account_name']
            df = valuation_df.loc[valuation_df.iloc[:, 0] == account_code, 'Net Market Value']
            if df.empty:
                continue
            else:
                fund_aum = df.values[0]
            aum_list.append([target_date, account_group_code, account_code, account_name, fund_aum, fund_aum])

    # 处理上海母公司发来的AUM文件(N个产品1个文件)-----------------------------------------------------
    print("Processing Headquarter Single AUM report...")
    hq_single_aum_files = find_specific_files_by_date(aum_report_path, hq_single_aum_file_regex, target_date.strftime('%Y%m%d'))

    if hq_single_aum_files is None:
        print("No Headquarter Single AUM report found for today, skip the process...")
        send2bark(bark_key, "AUM Automation", "No Headquarter Single AUM report found for today, skip the process...")
        exit(0)

    temp_account_code_mapping_df = account_code_mapping_df[account_code_mapping_df['code_type'] == 'hq_single'].reset_index(drop=True)

    for file in hq_single_aum_files:
        app = xlw.App(visible=False)
        wb = app.books.open(file)
        ws = wb.sheets[0]

        valuation_df = ws.range("A4").expand().options(pd.DataFrame, header=True, index=False).value
        # 确保关闭工作簿
        wb.close()

        valuation_df['基金代码'] = valuation_df['基金代码'].astype(str)

        for index, row in temp_account_code_mapping_df.iterrows():
            account_code = row['account_code']
            print("Processing account code: " + account_code)
            account_group_code = row['account_group_code']
            account_name = row['account_name']
            fund_aum = valuation_df.loc[valuation_df.iloc[:, 2] == account_code, 4].values[0]
            aum_list.append([target_date, account_group_code, account_code, account_name, fund_aum, fund_aum / fx_rate])

    aum_df = pd.DataFrame(aum_list, columns=['aum_date', 'account_group_code', 'account_code', 'account_name', 'aum_original', 'aum_local'])
    
    sql = f"delete from aum_record where aum_date = '{target_date.strftime('%Y-%m-%d')}'"
    db.execute_sql(sql)
    aum_df.to_sql('aum_record', db.db_engine, schema=None, if_exists='append', index=False, index_label=None, chunksize=None, dtype=None, method=None)

    aum_df = pd.DataFrame(aum_list, columns=['日期', '账户组代码', '账户代码', '账户名称', '账户规模（原币）', '账户规模（本币）'])
    aum_df =  aum_df.groupby(['账户组代码']).sum().reset_index()

    total_row = pd.Series([None] * len(aum_df.columns), index=aum_df.columns)
    total_row['aum_original'] = aum_df['aum_original'].sum()
    total_row['aum_local'] = aum_df['aum_local'].sum()
    total_row[aum_df.columns[-1]] = "合计"
    total_row.fillna('', inplace=True)
    aum_df.fillna('', inplace=True)
    aum_df.loc[len(aum_df)] = total_row

    # 创建一个格式化后的DataFrame副本
    formatted_aum_df = aum_df.copy()
    # 格式化数值列
    for col in ['账户规模（原币）', '账户规模（本币）']:
        if col in formatted_aum_df.columns:
            # 应用格式化函数
            formatted_aum_df[col] = formatted_aum_df[col].apply(
                lambda x: f'<span style="color:red">({abs(x):,.2f})</span>' if isinstance(x, (int, float)) and x < 0
                else f'{x:,.2f}' if isinstance(x, (int, float))
                else x
            )

    df_html = ""
    df_html += "<h3>账户规模</h3>"
    if not formatted_aum_df.empty:
        df_html += formatted_aum_df.to_html(
            index=False,
            escape=False  # 必须设置为False以允许HTML标签
        ) + "<br><br>"

    head = \
        """
        <head>
            <meta charset="utf-8">
            <STYLE TYPE="text/css" MEDIA=screen>

                table.dataframe {
                    border-collapse: collapse;
                    border: 2px solid #a19da2;
                    /*居中显示整个表格*/
                    margin: auto;
                }

                table.dataframe thead {
                    border: 2px solid #91c6e1;
                    background: #f1f1f1;
                    padding: 10px 10px 10px 10px;
                    color: #333333;
                }

                table.dataframe tbody {
                    border: 2px solid #91c6e1;
                    padding: 10px 10px 10px 10px;
                }

                table.dataframe tr {

                }

                table.dataframe th {
                    vertical-align: top;
                    font-size: 14px;
                    padding: 10px 10px 10px 10px;
                    color: #105de3;
                    font-family: arial;
                    text-align: center;
                }

                table.dataframe td {
                    text-align: center;
                    padding: 10px 10px 10px 10px;
                }

                body {
                    font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
                }

                h1 {
                    color: #5db446;
                    font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
                }

                div.header h2 {
                    color: #0002e3;
                    font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
                }

                div.content h2 {
                    text-align: center;
                    font-size: 28px;
                    text-shadow: 2px 2px 1px #de4040;
                    color: #fff;
                    font-weight: bold;
                    background-color: #008eb7;
                    line-height: 1.5;
                    margin: 20px 0;
                    box-shadow: 10px 10px 5px #888888;
                    border-radius: 5px;
                    font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
                }

                h3 {
                    font-size: 22px;
                    background-color: rgba(0, 2, 227, 0.71);
                    text-shadow: 2px 2px 1px #de4040;
                    color: rgba(239, 241, 234, 0.99);
                    line-height: 1.5;
                    font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
                }

                h4 {
                    color: #e10092;
                    font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
                    font-size: 20px;
                    text-align: left;
                }

                td img {
                    /*width: 60px;*/
                    max-width: 300px;
                    max-height: 300px;
                }

            </STYLE>
        </head>
        """

    body = \
        """
        <body>
        <div align="center" class="header">
            <!--标题部分的信息-->
            <h1 align="left">每日账户管理规模</h1>
            <h2 align="left">{aum_date}</h2>
        </div>
        <h4><font color="red">使用汇率：{fx_rate}</font></h4>
        <h4><font color="red">{warning_msg}</font></h4>
        <hr>
        <div class="content">
            <!--正文内容-->
            <h2> </h2>
            <div>
                <h4></h4>
                {df_html}
            </div>
            <hr>
            <p style="text-align: center">
            </p>
            <br />
        </div>
        </body>
        """.format(aum_date=target_date.strftime('%Y-%m-%d'), fx_rate=fx_rate, warning_msg=warning_msg, df_html=df_html)

    html_msg = "<html>" + head + body + "</html>"
    html_msg = html_msg.replace('\n', '').encode("utf-8")

    mail_nav_content = html_msg
    mail_nav_subject = Header("每日账户管理规模 - " + target_date.strftime('%Y-%m-%d'), 'utf-8')

    try:
        sendEmail(mail_auth_info, mail_sender, mail_receivers, mail_nav_subject, "", mail_nav_content, "")
        time.sleep(5)

        # 退出
        # smtpObj.quit()
        print('每日账户管理规模邮件发送成功！')

    except smtplib.SMTPException as e:
        print('mail_error', e)  # 打印错误





